{"code": 0, "zpData": {"position": [{"code": 1010000, "subLevelModelList": [{"code": 1000020, "subLevelModelList": [{"code": 100101, "subLevelModelList": [{"code": 6666600040, "positionType": 4, "level": 4, "name": "软件工程师（Java 方向）"}], "positionType": 3, "level": 3, "name": "Java", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100102, "positionType": 3, "level": 3, "name": "C/C++", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100103, "positionType": 3, "level": 3, "name": "PHP", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100109, "positionType": 3, "level": 3, "name": "Python", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100106, "positionType": 3, "level": 3, "name": "C#", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100107, "positionType": 3, "level": 3, "name": ".NET", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100116, "positionType": 3, "level": 3, "name": "Golang", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100114, "positionType": 3, "level": 3, "name": "Node.js", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100121, "subLevelModelList": [{"code": 6666600014, "positionType": 4, "level": 4, "name": "虚拟现实工程师"}], "positionType": 3, "level": 3, "name": "语音/视频/图形开发", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101313, "subLevelModelList": [{"code": 6666600668, "positionType": 4, "level": 4, "name": "系统运维工程师"}, {"code": 6666600857, "positionType": 4, "level": 4, "name": "计算机系统集成工程师"}, {"code": 6666601083, "positionType": 4, "level": 4, "name": "AI 算法工程师"}], "positionType": 3, "level": 3, "name": "高性能计算工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100124, "positionType": 3, "level": 3, "name": "GIS工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100125, "positionType": 3, "level": 3, "name": "区块链工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100123, "positionType": 3, "level": 3, "name": "全栈工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100199, "positionType": 3, "level": 3, "name": "其他后端开发", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "后端开发", "searchKey": "hulianwangAI"}, {"code": 1000030, "subLevelModelList": [{"code": 100901, "subLevelModelList": [{"code": 6666600022, "positionType": 4, "level": 4, "name": "网页前端开发工程师"}, {"code": 6666600253, "positionType": 4, "level": 4, "name": "软件研发工程师"}, {"code": 6666600413, "positionType": 4, "level": 4, "name": "前端开发工程师"}, {"code": 6666600532, "positionType": 4, "level": 4, "name": "物联网开发工程师"}, {"code": 6666600985, "positionType": 4, "level": 4, "name": "技术开发工程师"}, {"code": 6666601002, "positionType": 4, "level": 4, "name": "嵌入式开发工程师"}, {"code": 6666601041, "positionType": 4, "level": 4, "name": "技术研发工程师"}], "positionType": 3, "level": 3, "name": "前端开发工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100202, "positionType": 3, "level": 3, "name": "Android", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100203, "positionType": 3, "level": 3, "name": "iOS", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100209, "positionType": 3, "level": 3, "name": "U3D", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100211, "positionType": 3, "level": 3, "name": "UE4", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100210, "positionType": 3, "level": 3, "name": "Cocos", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100212, "positionType": 3, "level": 3, "name": "技术美术", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100208, "positionType": 3, "level": 3, "name": "JavaScript", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100213, "positionType": 3, "level": 3, "name": "鸿蒙开发工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "前端/移动开发", "searchKey": "hulianwangAI"}, {"code": 1000040, "subLevelModelList": [{"code": 100301, "positionType": 3, "level": 3, "name": "测试工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100309, "positionType": 3, "level": 3, "name": "软件测试", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100302, "positionType": 3, "level": 3, "name": "自动化测试", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100303, "positionType": 3, "level": 3, "name": "功能测试", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100305, "positionType": 3, "level": 3, "name": "测试开发", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100308, "positionType": 3, "level": 3, "name": "硬件测试", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100307, "positionType": 3, "level": 3, "name": "游戏测试", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100304, "subLevelModelList": [{"code": 6666600291, "positionType": 4, "level": 4, "name": "电子产品测试工程师"}], "positionType": 3, "level": 3, "name": "性能测试", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100310, "positionType": 3, "level": 3, "name": "渗透测试", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100703, "positionType": 3, "level": 3, "name": "测试经理", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "测试", "searchKey": "hulianwangAI"}, {"code": 1000050, "subLevelModelList": [{"code": 100401, "subLevelModelList": [{"code": 6666600162, "positionType": 4, "level": 4, "name": "教育技术运维工程师"}, {"code": 6666600168, "positionType": 4, "level": 4, "name": "货运车辆车联网工程师"}, {"code": 6666601084, "positionType": 4, "level": 4, "name": "运维工程师"}], "positionType": 3, "level": 3, "name": "运维工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100405, "positionType": 3, "level": 3, "name": "IT技术支持", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100403, "subLevelModelList": [{"code": 6666600739, "positionType": 4, "level": 4, "name": "网络安全工程师"}, {"code": 6666600748, "positionType": 4, "level": 4, "name": "网络工程师"}], "positionType": 3, "level": 3, "name": "网络工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100407, "positionType": 3, "level": 3, "name": "网络安全", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100404, "subLevelModelList": [{"code": 6666600097, "positionType": 4, "level": 4, "name": "技术服务工程师"}, {"code": 6666600373, "positionType": 4, "level": 4, "name": "系统集成工程师"}, {"code": 6666600395, "positionType": 4, "level": 4, "name": "制冷系统研发工程师"}], "positionType": 3, "level": 3, "name": "系统工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100402, "positionType": 3, "level": 3, "name": "运维开发工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100406, "subLevelModelList": [{"code": 6666600019, "positionType": 4, "level": 4, "name": "后勤管理员"}, {"code": 6666600602, "positionType": 4, "level": 4, "name": "工程合同管理员"}, {"code": 6666600890, "positionType": 4, "level": 4, "name": "租赁合约管理员"}], "positionType": 3, "level": 3, "name": "系统管理员", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100409, "positionType": 3, "level": 3, "name": "DBA", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 290166, "positionType": 3, "level": 3, "name": "电脑/打印机维修", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100408, "positionType": 3, "level": 3, "name": "系统安全", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100410, "subLevelModelList": [{"code": 6666601009, "positionType": 4, "level": 4, "name": "文档工程师"}], "positionType": 3, "level": 3, "name": "技术文档工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "运维/技术支持", "searchKey": "hulianwangAI"}, {"code": 1000130, "subLevelModelList": [{"code": 101306, "positionType": 3, "level": 3, "name": "图像算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100117, "positionType": 3, "level": 3, "name": "自然语言处理算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101310, "positionType": 3, "level": 3, "name": "大模型算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100104, "positionType": 3, "level": 3, "name": "数据挖掘", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101311, "positionType": 3, "level": 3, "name": "规控算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101312, "positionType": 3, "level": 3, "name": "SLAM算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100118, "positionType": 3, "level": 3, "name": "推荐算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100115, "positionType": 3, "level": 3, "name": "搜索算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101305, "positionType": 3, "level": 3, "name": "语音算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101309, "positionType": 3, "level": 3, "name": "风控算法", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101313, "subLevelModelList": [{"code": 6666600668, "positionType": 4, "level": 4, "name": "系统运维工程师"}, {"code": 6666600857, "positionType": 4, "level": 4, "name": "计算机系统集成工程师"}, {"code": 6666601083, "positionType": 4, "level": 4, "name": "AI 算法工程师"}], "positionType": 3, "level": 3, "name": "高性能计算工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100120, "subLevelModelList": [{"code": 6666600143, "positionType": 4, "level": 4, "name": "大数据开发工程师"}, {"code": 6666600290, "positionType": 4, "level": 4, "name": "人工智能开发工程师"}], "positionType": 3, "level": 3, "name": "算法工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101307, "subLevelModelList": [{"code": 6666600482, "positionType": 4, "level": 4, "name": "教育 AI 应用研究员"}], "positionType": 3, "level": 3, "name": "算法研究员", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101301, "subLevelModelList": [{"code": 6666600082, "positionType": 4, "level": 4, "name": "机器人工程师"}], "positionType": 3, "level": 3, "name": "机器学习", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101302, "positionType": 3, "level": 3, "name": "深度学习", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101308, "subLevelModelList": [{"code": 6666601064, "positionType": 4, "level": 4, "name": "智能驾驶系统开发工程师"}], "positionType": 3, "level": 3, "name": "自动驾驶系统工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 130121, "positionType": 3, "level": 3, "name": "数据标注/AI训练师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "人工智能", "searchKey": "hulianwangAI"}, {"code": 1000140, "subLevelModelList": [{"code": 101201, "subLevelModelList": [{"code": 6666600860, "positionType": 4, "level": 4, "name": "渠道销售专员（批发商 / 零售商）"}, {"code": 6666601047, "positionType": 4, "level": 4, "name": "建筑图书销售代表"}, {"code": 6666601098, "positionType": 4, "level": 4, "name": "技术服务经理"}], "positionType": 3, "level": 3, "name": "售前技术支持", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101202, "subLevelModelList": [{"code": 6666600357, "positionType": 4, "level": 4, "name": "售后技术支持工程师"}, {"code": 6666600402, "positionType": 4, "level": 4, "name": "家具售后工程师/安装维修技师"}, {"code": 6666600464, "positionType": 4, "level": 4, "name": "售后服务主管"}, {"code": 6666600898, "positionType": 4, "level": 4, "name": "售后服务工程师"}], "positionType": 3, "level": 3, "name": "售后技术支持", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 101299, "subLevelModelList": [{"code": 6666600122, "positionType": 4, "level": 4, "name": "混凝土设备安装调试员"}, {"code": 6666600726, "positionType": 4, "level": 4, "name": "空压机租赁技术支持"}], "positionType": 3, "level": 3, "name": "销售技术支持", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 160303, "subLevelModelList": [{"code": 6666600048, "positionType": 4, "level": 4, "name": "建筑机械租赁客服经理"}, {"code": 6666600059, "positionType": 4, "level": 4, "name": "模架设备租赁经理"}, {"code": 6666600071, "positionType": 4, "level": 4, "name": "租赁销售经理"}, {"code": 6666600102, "positionType": 4, "level": 4, "name": "建筑工程机械与设备租赁"}, {"code": 6666600149, "positionType": 4, "level": 4, "name": "建筑工程机械租赁专员"}, {"code": 6666600184, "positionType": 4, "level": 4, "name": "土方机械租赁专员"}, {"code": 6666600362, "positionType": 4, "level": 4, "name": "销售客户成功经理"}, {"code": 6666600411, "positionType": 4, "level": 4, "name": "客户成功顾问"}], "positionType": 3, "level": 3, "name": "客户成功", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "销售技术支持", "searchKey": "hulianwangAI"}, {"code": 1000060, "subLevelModelList": [{"code": 100511, "subLevelModelList": [{"code": 6666600064, "positionType": 4, "level": 4, "name": "食品销售区域分析师"}, {"code": 6666600107, "positionType": 4, "level": 4, "name": "教育舆情分析师"}, {"code": 6666600228, "positionType": 4, "level": 4, "name": "销售数据分析主管"}, {"code": 6666600232, "positionType": 4, "level": 4, "name": "房地产投资分析师"}, {"code": 6666600284, "positionType": 4, "level": 4, "name": "数据分析专员"}, {"code": 6666600374, "positionType": 4, "level": 4, "name": "建筑销售行业分析师"}, {"code": 6666600542, "positionType": 4, "level": 4, "name": " 租赁客户信用分析师"}, {"code": 6666600750, "positionType": 4, "level": 4, "name": "教育碳足迹分析师"}, {"code": 6666600771, "positionType": 4, "level": 4, "name": "市场调研分析师"}, {"code": 6666600791, "positionType": 4, "level": 4, "name": "销售数据分析专员"}, {"code": 6666601049, "positionType": 4, "level": 4, "name": "数据分析师"}], "positionType": 3, "level": 3, "name": "数据分析师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100508, "positionType": 3, "level": 3, "name": "数据开发", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100507, "positionType": 3, "level": 3, "name": "数据仓库", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100506, "positionType": 3, "level": 3, "name": "ETL工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100104, "positionType": 3, "level": 3, "name": "数据挖掘", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100512, "subLevelModelList": [{"code": 6666600863, "positionType": 4, "level": 4, "name": "工业互联网架构师"}], "positionType": 3, "level": 3, "name": "数据架构师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100514, "positionType": 3, "level": 3, "name": "爬虫工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100122, "positionType": 3, "level": 3, "name": "数据采集", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100515, "positionType": 3, "level": 3, "name": "数据治理", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "数据", "searchKey": "hulianwangAI"}, {"code": 1000070, "subLevelModelList": [{"code": 100601, "subLevelModelList": [{"code": 6666600039, "positionType": 4, "level": 4, "name": "新能源技术转化经理"}, {"code": 6666600189, "positionType": 4, "level": 4, "name": "建筑工程技术负责人"}, {"code": 6666600287, "positionType": 4, "level": 4, "name": "项目经理（软件方向）"}, {"code": 6666600438, "positionType": 4, "level": 4, "name": "研发项目主管"}, {"code": 6666600664, "positionType": 4, "level": 4, "name": "技术商业化经理"}, {"code": 6666600680, "positionType": 4, "level": 4, "name": "发电机租赁项目经理"}, {"code": 6666600763, "positionType": 4, "level": 4, "name": "工程施工项目经理"}, {"code": 6666600776, "positionType": 4, "level": 4, "name": "钢筋工长"}, {"code": 6666600825, "positionType": 4, "level": 4, "name": "设备租赁项目经理"}, {"code": 6666600842, "positionType": 4, "level": 4, "name": "技术转化项目经理"}, {"code": 6666600991, "positionType": 4, "level": 4, "name": "危化品经营经理/合规主管"}, {"code": 6666601068, "positionType": 4, "level": 4, "name": "项目经理（工程方向）"}], "positionType": 3, "level": 3, "name": "项目经理/主管", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100606, "subLevelModelList": [{"code": 6666600498, "positionType": 4, "level": 4, "name": "安装工程师"}, {"code": 6666600894, "positionType": 4, "level": 4, "name": "产品研发工程师（传动部件）"}, {"code": 6666601057, "positionType": 4, "level": 4, "name": "模具加工工艺工程师"}], "positionType": 3, "level": 3, "name": "实施工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100605, "subLevelModelList": [{"code": 6666600566, "positionType": 4, "level": 4, "name": "技术咨询顾问"}], "positionType": 3, "level": 3, "name": "实施顾问", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100607, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "食品调味品数据分析师"}, {"code": **********, "positionType": 4, "level": 4, "name": "设备租赁报价工程师"}], "positionType": 3, "level": 3, "name": "需求分析工程师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100817, "subLevelModelList": [{"code": 6666600696, "positionType": 4, "level": 4, "name": "设备经理"}], "positionType": 3, "level": 3, "name": "硬件项目经理", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100603, "subLevelModelList": [{"code": 6666600024, "positionType": 4, "level": 4, "name": "咨询策划专员"}, {"code": 6666600314, "positionType": 4, "level": 4, "name": "信息管理专员"}, {"code": 6666600675, "positionType": 4, "level": 4, "name": "进出口业务专员"}, {"code": 6666600703, "positionType": 4, "level": 4, "name": "会议服务专员"}, {"code": 6666600722, "positionType": 4, "level": 4, "name": "租赁风控专员"}, {"code": 6666600832, "positionType": 4, "level": 4, "name": "项目主管"}, {"code": 6666600886, "positionType": 4, "level": 4, "name": "服务合规专员"}, {"code": 6666600917, "positionType": 4, "level": 4, "name": "物流项目专员"}, {"code": 6666600949, "positionType": 4, "level": 4, "name": "工会专员"}], "positionType": 3, "level": 3, "name": "项目专员/助理", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "技术项目管理", "searchKey": "hulianwangAI"}, {"code": 1000120, "subLevelModelList": [{"code": 100701, "subLevelModelList": [{"code": 6666600633, "positionType": 4, "level": 4, "name": "技术顾问​"}, {"code": 6666600753, "positionType": 4, "level": 4, "name": "技术负责人"}], "positionType": 3, "level": 3, "name": "技术经理", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100704, "positionType": 3, "level": 3, "name": "架构师", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100702, "subLevelModelList": [{"code": 6666600026, "positionType": 4, "level": 4, "name": "技术研发总监（模具 / 电子）"}, {"code": 6666600764, "positionType": 4, "level": 4, "name": "技术研发总监"}], "positionType": 3, "level": 3, "name": "技术总监", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100705, "positionType": 3, "level": 3, "name": "CTO/CIO", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100707, "positionType": 3, "level": 3, "name": "技术合伙人", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}, {"code": 100706, "positionType": 3, "level": 3, "name": "运维总监", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "高端技术职位", "searchKey": "hulianwangAI"}, {"code": 1000150, "subLevelModelList": [{"code": 101101, "subLevelModelList": [{"code": 6666600572, "positionType": 4, "level": 4, "name": "烘焙工程师"}, {"code": 6666600583, "positionType": 4, "level": 4, "name": "门窗安装技师"}, {"code": 6666600586, "positionType": 4, "level": 4, "name": "首席维修工程师"}, {"code": 6666600770, "positionType": 4, "level": 4, "name": "技术服务专员"}, {"code": 6666600866, "positionType": 4, "level": 4, "name": "能源管理工程师"}, {"code": 6666600895, "positionType": 4, "level": 4, "name": "环保监测员"}, {"code": 6666600935, "positionType": 4, "level": 4, "name": "机械零部件工艺工程师"}, {"code": 6666600967, "positionType": 4, "level": 4, "name": "五金加工工艺工程师"}, {"code": 6666600972, "positionType": 4, "level": 4, "name": "计算机技术支持工程师岗位"}, {"code": 6666600986, "positionType": 4, "level": 4, "name": "水质检验工"}, {"code": 6666601101, "positionType": 4, "level": 4, "name": "试验员"}], "positionType": 3, "level": 3, "name": "其他技术职位", "searchKey": "hulianwangAI", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他技术职位", "searchKey": "hulianwangAI"}], "positionType": 0, "level": 1, "name": "互联网/AI", "searchKey": "hulianwangAI"}, {"code": 1210000, "subLevelModelList": [{"code": 1000080, "subLevelModelList": [{"code": 101401, "subLevelModelList": [{"code": 6666600157, "positionType": 4, "level": 4, "name": "电子产品结构设计师"}], "positionType": 3, "level": 3, "name": "电子工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100801, "subLevelModelList": [{"code": 6666600120, "positionType": 4, "level": 4, "name": "电子产品硬件工程师"}, {"code": 6666600185, "positionType": 4, "level": 4, "name": "硬件开发工程师"}], "positionType": 3, "level": 3, "name": "硬件工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100802, "subLevelModelList": [{"code": 6666600241, "positionType": 4, "level": 4, "name": "软件工程师"}, {"code": 6666600618, "positionType": 4, "level": 4, "name": "软件开发工程师"}, {"code": 6666600702, "positionType": 4, "level": 4, "name": "软件测试工程师"}], "positionType": 3, "level": 3, "name": "嵌入式软件工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100808, "positionType": 3, "level": 3, "name": "FPGA开发", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100804, "positionType": 3, "level": 3, "name": "单片机", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100806, "positionType": 3, "level": 3, "name": "驱动开发工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100811, "positionType": 3, "level": 3, "name": "PCB工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101408, "subLevelModelList": [{"code": 6666600772, "positionType": 4, "level": 4, "name": "3D打印模型处理技术员"}, {"code": 6666601054, "positionType": 4, "level": 4, "name": "表面处理技术员"}], "positionType": 3, "level": 3, "name": "电子维修技术员", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100816, "positionType": 3, "level": 3, "name": "射频工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100805, "positionType": 3, "level": 3, "name": "电路设计", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100807, "positionType": 3, "level": 3, "name": "系统集成", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100818, "positionType": 3, "level": 3, "name": "光学工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100809, "positionType": 3, "level": 3, "name": "DSP开发", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100819, "positionType": 3, "level": 3, "name": "电源工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300801, "positionType": 3, "level": 3, "name": "电池工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "电子/硬件开发", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000110, "subLevelModelList": [{"code": 101405, "positionType": 3, "level": 3, "name": "集成电路IC设计", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101406, "positionType": 3, "level": 3, "name": "数字IC验证工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101407, "positionType": 3, "level": 3, "name": "模拟版图设计工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101409, "positionType": 3, "level": 3, "name": "芯片测试工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101410, "positionType": 3, "level": 3, "name": "DFT工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101403, "positionType": 3, "level": 3, "name": "FAE", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101411, "positionType": 3, "level": 3, "name": "数字前端设计师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101412, "subLevelModelList": [{"code": 6666600701, "positionType": 4, "level": 4, "name": "数字孪生工程师"}], "positionType": 3, "level": 3, "name": "数字后端工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101413, "positionType": 3, "level": 3, "name": "模拟IC设计工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "半导体/芯片", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600842, "subLevelModelList": [{"code": 101402, "subLevelModelList": [{"code": 6666600042, "positionType": 4, "level": 4, "name": "仪器仪表技术支持工程师"}, {"code": 6666600302, "positionType": 4, "level": 4, "name": "电气工程师"}, {"code": 6666600334, "positionType": 4, "level": 4, "name": "变配电运行工"}, {"code": 6666600564, "positionType": 4, "level": 4, "name": "电气产品销售工程师"}, {"code": 6666601006, "positionType": 4, "level": 4, "name": "电气控制工程师"}], "positionType": 3, "level": 3, "name": "电气工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101404, "subLevelModelList": [{"code": 6666600237, "positionType": 4, "level": 4, "name": "PCB 设计工程师"}, {"code": 6666600268, "positionType": 4, "level": 4, "name": "新能源设备研发工程师"}], "positionType": 3, "level": 3, "name": "电气设计工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100803, "subLevelModelList": [{"code": 6666600056, "positionType": 4, "level": 4, "name": "仪器仪表技术支持"}, {"code": 6666600547, "positionType": 4, "level": 4, "name": "数控编程工程师（模具）"}, {"code": 6666600824, "positionType": 4, "level": 4, "name": "自动化控制工程师"}], "positionType": 3, "level": 3, "name": "自动化工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300310, "subLevelModelList": [{"code": 6666600145, "positionType": 4, "level": 4, "name": "电机驱动工程师"}, {"code": 6666600848, "positionType": 4, "level": 4, "name": "机电安装工长"}, {"code": 6666601059, "positionType": 4, "level": 4, "name": "机电工程师"}], "positionType": 3, "level": 3, "name": "机电工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220223, "subLevelModelList": [{"code": 6666600308, "positionType": 4, "level": 4, "name": "机电一体化工程师"}, {"code": 6666600816, "positionType": 4, "level": 4, "name": "建筑智能化调试员"}, {"code": 6666600819, "positionType": 4, "level": 4, "name": "隧道工程师"}, {"code": 6666601089, "positionType": 4, "level": 4, "name": "建筑机械租赁业务总监"}], "positionType": 3, "level": 3, "name": "建筑机电工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "电气/自动化", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600422, "subLevelModelList": [{"code": 100601, "subLevelModelList": [{"code": 6666600039, "positionType": 4, "level": 4, "name": "新能源技术转化经理"}, {"code": 6666600189, "positionType": 4, "level": 4, "name": "建筑工程技术负责人"}, {"code": 6666600287, "positionType": 4, "level": 4, "name": "项目经理（软件方向）"}, {"code": 6666600438, "positionType": 4, "level": 4, "name": "研发项目主管"}, {"code": 6666600664, "positionType": 4, "level": 4, "name": "技术商业化经理"}, {"code": 6666600680, "positionType": 4, "level": 4, "name": "发电机租赁项目经理"}, {"code": 6666600763, "positionType": 4, "level": 4, "name": "工程施工项目经理"}, {"code": 6666600776, "positionType": 4, "level": 4, "name": "钢筋工长"}, {"code": 6666600825, "positionType": 4, "level": 4, "name": "设备租赁项目经理"}, {"code": 6666600842, "positionType": 4, "level": 4, "name": "技术转化项目经理"}, {"code": 6666600991, "positionType": 4, "level": 4, "name": "危化品经营经理/合规主管"}, {"code": 6666601068, "positionType": 4, "level": 4, "name": "项目经理（工程方向）"}], "positionType": 3, "level": 3, "name": "项目经理/主管", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100606, "subLevelModelList": [{"code": 6666600498, "positionType": 4, "level": 4, "name": "安装工程师"}, {"code": 6666600894, "positionType": 4, "level": 4, "name": "产品研发工程师（传动部件）"}, {"code": 6666601057, "positionType": 4, "level": 4, "name": "模具加工工艺工程师"}], "positionType": 3, "level": 3, "name": "实施工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100605, "subLevelModelList": [{"code": 6666600566, "positionType": 4, "level": 4, "name": "技术咨询顾问"}], "positionType": 3, "level": 3, "name": "实施顾问", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100817, "subLevelModelList": [{"code": 6666600696, "positionType": 4, "level": 4, "name": "设备经理"}], "positionType": 3, "level": 3, "name": "硬件项目经理", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100603, "subLevelModelList": [{"code": 6666600024, "positionType": 4, "level": 4, "name": "咨询策划专员"}, {"code": 6666600314, "positionType": 4, "level": 4, "name": "信息管理专员"}, {"code": 6666600675, "positionType": 4, "level": 4, "name": "进出口业务专员"}, {"code": 6666600703, "positionType": 4, "level": 4, "name": "会议服务专员"}, {"code": 6666600722, "positionType": 4, "level": 4, "name": "租赁风控专员"}, {"code": 6666600832, "positionType": 4, "level": 4, "name": "项目主管"}, {"code": 6666600886, "positionType": 4, "level": 4, "name": "服务合规专员"}, {"code": 6666600917, "positionType": 4, "level": 4, "name": "物流项目专员"}, {"code": 6666600949, "positionType": 4, "level": 4, "name": "工会专员"}], "positionType": 3, "level": 3, "name": "项目专员/助理", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "技术项目管理", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000100, "subLevelModelList": [{"code": 101011, "positionType": 3, "level": 3, "name": "通信项目专员", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101012, "positionType": 3, "level": 3, "name": "通信项目经理", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101001, "subLevelModelList": [{"code": 6666600004, "positionType": 4, "level": 4, "name": "技术开发与咨询工程师"}, {"code": 6666600501, "positionType": 4, "level": 4, "name": "通信技术工程师"}], "positionType": 3, "level": 3, "name": "通信技术工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101002, "subLevelModelList": [{"code": 6666600989, "positionType": 4, "level": 4, "name": "研发总监"}], "positionType": 3, "level": 3, "name": "通信研发工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101003, "positionType": 3, "level": 3, "name": "数据通信工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101018, "positionType": 3, "level": 3, "name": "光网络工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101007, "positionType": 3, "level": 3, "name": "有线传输工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101013, "positionType": 3, "level": 3, "name": "核心网工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101010, "subLevelModelList": [{"code": 6666600731, "positionType": 4, "level": 4, "name": "标准化工程师"}], "positionType": 3, "level": 3, "name": "通信标准化工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100403, "subLevelModelList": [{"code": 6666600739, "positionType": 4, "level": 4, "name": "网络安全工程师"}, {"code": 6666600748, "positionType": 4, "level": 4, "name": "网络工程师"}], "positionType": 3, "level": 3, "name": "网络工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101005, "positionType": 3, "level": 3, "name": "宽带装维", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101008, "positionType": 3, "level": 3, "name": "无线/天线工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "通信", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600862, "subLevelModelList": [{"code": 101201, "subLevelModelList": [{"code": 6666600860, "positionType": 4, "level": 4, "name": "渠道销售专员（批发商 / 零售商）"}, {"code": 6666601047, "positionType": 4, "level": 4, "name": "建筑图书销售代表"}, {"code": 6666601098, "positionType": 4, "level": 4, "name": "技术服务经理"}], "positionType": 3, "level": 3, "name": "售前技术支持", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101202, "subLevelModelList": [{"code": 6666600357, "positionType": 4, "level": 4, "name": "售后技术支持工程师"}, {"code": 6666600402, "positionType": 4, "level": 4, "name": "家具售后工程师/安装维修技师"}, {"code": 6666600464, "positionType": 4, "level": 4, "name": "售后服务主管"}, {"code": 6666600898, "positionType": 4, "level": 4, "name": "售后服务工程师"}], "positionType": 3, "level": 3, "name": "售后技术支持", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101299, "subLevelModelList": [{"code": 6666600122, "positionType": 4, "level": 4, "name": "混凝土设备安装调试员"}, {"code": 6666600726, "positionType": 4, "level": 4, "name": "空压机租赁技术支持"}], "positionType": 3, "level": 3, "name": "销售技术支持", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "销售技术支持", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600852, "subLevelModelList": [{"code": 100401, "subLevelModelList": [{"code": 6666600162, "positionType": 4, "level": 4, "name": "教育技术运维工程师"}, {"code": 6666600168, "positionType": 4, "level": 4, "name": "货运车辆车联网工程师"}, {"code": 6666601084, "positionType": 4, "level": 4, "name": "运维工程师"}], "positionType": 3, "level": 3, "name": "运维工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100404, "subLevelModelList": [{"code": 6666600097, "positionType": 4, "level": 4, "name": "技术服务工程师"}, {"code": 6666600373, "positionType": 4, "level": 4, "name": "系统集成工程师"}, {"code": 6666600395, "positionType": 4, "level": 4, "name": "制冷系统研发工程师"}], "positionType": 3, "level": 3, "name": "系统工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100410, "subLevelModelList": [{"code": 6666601009, "positionType": 4, "level": 4, "name": "文档工程师"}], "positionType": 3, "level": 3, "name": "技术文档工程师", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "运维/技术支持", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "电子/电气/通信", "searchKey": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1020000, "subLevelModelList": [{"code": 1000160, "subLevelModelList": [{"code": 110101, "subLevelModelList": [{"code": 6666600111, "positionType": 4, "level": 4, "name": "产品经理（工具类）"}, {"code": 6666600217, "positionType": 4, "level": 4, "name": "产品经理（软件方向）"}, {"code": 6666600425, "positionType": 4, "level": 4, "name": "合约经理"}, {"code": 6666600462, "positionType": 4, "level": 4, "name": "母婴健康服务产品经理"}, {"code": **********, "positionType": 4, "level": 4, "name": "产品经理（设备方向）"}, {"code": **********, "positionType": 4, "level": 4, "name": "销售产品经理"}, {"code": **********, "positionType": 4, "level": 4, "name": "租赁设备研发经理"}, {"code": **********, "positionType": 4, "level": 4, "name": "产品经理（仪器仪表）"}, {"code": **********, "positionType": 4, "level": 4, "name": "产品经理"}], "positionType": 3, "level": 3, "name": "产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110108, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "五金机电产品专员"}, {"code": **********, "positionType": 4, "level": 4, "name": "加工专员"}, {"code": **********, "positionType": 4, "level": 4, "name": "果蔬产销专员"}, {"code": **********, "positionType": 4, "level": 4, "name": "机械设备租赁专员"}, {"code": **********, "positionType": 4, "level": 4, "name": "文化艺术交流活动策划执行专员"}], "positionType": 3, "level": 3, "name": "产品专员/助理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110302, "positionType": 3, "level": 3, "name": "高级产品管理岗", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 120302, "positionType": 3, "level": 3, "name": "用户研究", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110106, "positionType": 3, "level": 3, "name": "电商产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110110, "positionType": 3, "level": 3, "name": "AI产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110105, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "数据中心经理"}, {"code": **********, "positionType": 4, "level": 4, "name": "信息系统集成项目经理"}], "positionType": 3, "level": 3, "name": "数据产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110103, "positionType": 3, "level": 3, "name": "移动产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 180501, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "金融产品经理"}, {"code": **********, "positionType": 4, "level": 4, "name": "资产管理员"}], "positionType": 3, "level": 3, "name": "金融产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 130133, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "跨境仪器贸易主管"}], "positionType": 3, "level": 3, "name": "跨境电商产品开发", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110111, "positionType": 3, "level": 3, "name": "化妆品产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110109, "positionType": 3, "level": 3, "name": "硬件产品经理", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110401, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "3D打印质量控制员"}], "positionType": 3, "level": 3, "name": "其他产品职位", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 100607, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "食品调味品数据分析师"}, {"code": **********, "positionType": 4, "level": 4, "name": "设备租赁报价工程师"}], "positionType": 3, "level": 3, "name": "需求分析工程师", "searchKey": "chanpin", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "产品经理", "searchKey": "chanpin"}, {"code": 2600432, "subLevelModelList": [{"code": 110107, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "游戏开发工程师"}], "positionType": 3, "level": 3, "name": "游戏策划", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 120305, "positionType": 3, "level": 3, "name": "系统策划", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 120303, "positionType": 3, "level": 3, "name": "游戏数值策划", "searchKey": "chanpin", "recruitmentType": "1,2,3"}, {"code": 110303, "positionType": 3, "level": 3, "name": "游戏制作人", "searchKey": "chanpin", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "游戏策划/制作", "searchKey": "chanpin"}], "positionType": 0, "level": 1, "name": "产品", "searchKey": "chanpin"}, {"code": 1040000, "subLevelModelList": [{"code": 1000270, "subLevelModelList": [{"code": 130305, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "电商客服专员"}, {"code": **********, "positionType": 4, "level": 4, "name": "客服专员"}, {"code": **********, "positionType": 4, "level": 4, "name": "客户服务专员"}, {"code": **********, "positionType": 4, "level": 4, "name": "租赁客服经理"}, {"code": **********, "positionType": 4, "level": 4, "name": "食品电商客服组长"}], "positionType": 3, "level": 3, "name": "客服专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130306, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "客服主管"}], "positionType": 3, "level": 3, "name": "客服主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130304, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "电商客服组长（食品调味品）"}], "positionType": 3, "level": 3, "name": "客服经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130303, "positionType": 3, "level": 3, "name": "网络客服", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130308, "positionType": 3, "level": 3, "name": "电话客服", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130302, "subLevelModelList": [{"code": 6666600139, "positionType": 4, "level": 4, "name": "售后服务经理"}, {"code": 6666600315, "positionType": 4, "level": 4, "name": "设备租赁客户满意度专员"}, {"code": 6666600378, "positionType": 4, "level": 4, "name": "售后专员"}, {"code": 6666600984, "positionType": 4, "level": 4, "name": "销售客服专员"}, {"code": 6666601034, "positionType": 4, "level": 4, "name": "售后服务专员"}], "positionType": 3, "level": 3, "name": "售后客服", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130301, "subLevelModelList": [{"code": 6666600914, "positionType": 4, "level": 4, "name": "机械设备售后服务工程师"}], "positionType": 3, "level": 3, "name": "售前客服", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "客服", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001290, "subLevelModelList": [{"code": 130111, "subLevelModelList": [{"code": 6666600714, "positionType": 4, "level": 4, "name": "教育虚拟人 IP 运营"}], "positionType": 3, "level": 3, "name": "新媒体运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130122, "subLevelModelList": [{"code": 6666600166, "positionType": 4, "level": 4, "name": "直播运营专员（食品带货）"}, {"code": 6666600809, "positionType": 4, "level": 4, "name": "教育私域流量运营主管"}, {"code": 6666601070, "positionType": 4, "level": 4, "name": "新媒体运营专员"}], "positionType": 3, "level": 3, "name": "直播运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170108, "positionType": 3, "level": 3, "name": "视频运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130104, "positionType": 3, "level": 3, "name": "内容运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130113, "positionType": 3, "level": 3, "name": "微信运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "内容运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001300, "subLevelModelList": [{"code": 130117, "subLevelModelList": [{"code": 6666600043, "positionType": 4, "level": 4, "name": "五金电商运营专员"}, {"code": 6666600335, "positionType": 4, "level": 4, "name": "电商运营"}, {"code": 6666600474, "positionType": 4, "level": 4, "name": "教育电商运营专员"}, {"code": 6666600573, "positionType": 4, "level": 4, "name": "电商运营经理"}, {"code": 6666600639, "positionType": 4, "level": 4, "name": "电商运营专员（健康产品方向）"}, {"code": 6666600784, "positionType": 4, "level": 4, "name": "电商运营专员"}, {"code": 6666600892, "positionType": 4, "level": 4, "name": "电商运营专员​"}, {"code": 6666600961, "positionType": 4, "level": 4, "name": "机械设备电商运营"}], "positionType": 3, "level": 3, "name": "国内电商运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130124, "subLevelModelList": [{"code": 6666600052, "positionType": 4, "level": 4, "name": "教育跨境电商运营"}, {"code": 6666600086, "positionType": 4, "level": 4, "name": "食品跨境电商运营专员"}, {"code": 6666600163, "positionType": 4, "level": 4, "name": "跨境电商运营主管"}, {"code": 6666600549, "positionType": 4, "level": 4, "name": "跨境电商运营"}], "positionType": 3, "level": 3, "name": "跨境电商运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130107, "subLevelModelList": [{"code": 6666600508, "positionType": 4, "level": 4, "name": "互联网运营专员"}], "positionType": 3, "level": 3, "name": "品类运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130126, "positionType": 3, "level": 3, "name": "淘宝运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130127, "positionType": 3, "level": 3, "name": "天猫运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130128, "positionType": 3, "level": 3, "name": "京东运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130129, "positionType": 3, "level": 3, "name": "拼多多运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130130, "positionType": 3, "level": 3, "name": "亚马逊运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130133, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "跨境仪器贸易主管"}], "positionType": 3, "level": 3, "name": "跨境电商产品开发", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130132, "positionType": 3, "level": 3, "name": "阿里国际站运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130131, "positionType": 3, "level": 3, "name": "速卖通运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "电商运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000250, "subLevelModelList": [{"code": 130118, "subLevelModelList": [{"code": 6666600147, "positionType": 4, "level": 4, "name": "教育直播运营助理"}, {"code": 6666600710, "positionType": 4, "level": 4, "name": "商务代理与资产运营专员"}, {"code": 6666600734, "positionType": 4, "level": 4, "name": "建筑机械租赁数字化运营专员"}], "positionType": 3, "level": 3, "name": "运营助理/专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130102, "subLevelModelList": [{"code": 6666600158, "positionType": 4, "level": 4, "name": "食品电商销售主管（全品类"}, {"code": 6666600370, "positionType": 4, "level": 4, "name": "休闲食品经销商运营专员"}, {"code": 6666600426, "positionType": 4, "level": 4, "name": "销售运营专员"}], "positionType": 3, "level": 3, "name": "产品运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130101, "positionType": 3, "level": 3, "name": "用户运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130106, "positionType": 3, "level": 3, "name": "商家运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130103, "positionType": 3, "level": 3, "name": "数据/策略运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130112, "subLevelModelList": [{"code": 6666600456, "positionType": 4, "level": 4, "name": "教育社群运营主管"}], "positionType": 3, "level": 3, "name": "社区运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130108, "positionType": 3, "level": 3, "name": "游戏运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130105, "positionType": 3, "level": 3, "name": "活动运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130110, "positionType": 3, "level": 3, "name": "网站运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130120, "subLevelModelList": [{"code": 6666600670, "positionType": 4, "level": 4, "name": "教育内容审核专员"}], "positionType": 3, "level": 3, "name": "内容审核", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130121, "positionType": 3, "level": 3, "name": "数据标注/AI训练师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130134, "positionType": 3, "level": 3, "name": "外卖运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "业务运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001310, "subLevelModelList": [{"code": 130123, "positionType": 3, "level": 3, "name": "车辆运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130116, "positionType": 3, "level": 3, "name": "线下拓展运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290314, "subLevelModelList": [{"code": 6666600850, "positionType": 4, "level": 4, "name": "体育场馆运营专员"}, {"code": 6666600956, "positionType": 4, "level": 4, "name": "运营专员"}], "positionType": 3, "level": 3, "name": "商场运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "线下运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000260, "subLevelModelList": [{"code": 130203, "positionType": 3, "level": 3, "name": "文案编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130201, "positionType": 3, "level": 3, "name": "主编/副主编", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130204, "positionType": 3, "level": 3, "name": "网站编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210101, "positionType": 3, "level": 3, "name": "医学编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000280, "subLevelModelList": [{"code": 130405, "subLevelModelList": [{"code": 6666600020, "positionType": 4, "level": 4, "name": "建筑销售新媒体运营经理"}, {"code": 6666600058, "positionType": 4, "level": 4, "name": "调味品私域运营经理"}, {"code": 6666600072, "positionType": 4, "level": 4, "name": "电商运营经理（农产品旗舰店）"}, {"code": 6666600161, "positionType": 4, "level": 4, "name": "商业综合体运营经理"}, {"code": 6666600179, "positionType": 4, "level": 4, "name": "项目运营经理"}, {"code": 6666600354, "positionType": 4, "level": 4, "name": "运营主管"}, {"code": 6666600407, "positionType": 4, "level": 4, "name": "网络货运运营经理"}, {"code": 6666600446, "positionType": 4, "level": 4, "name": "垃圾焚烧发电运营主管"}, {"code": 6666600873, "positionType": 4, "level": 4, "name": "调味品电商运营经理"}, {"code": 6666600907, "positionType": 4, "level": 4, "name": "运营副总经理"}, {"code": 6666600916, "positionType": 4, "level": 4, "name": "电商运营经理（食品旗舰店）"}, {"code": 6666600963, "positionType": 4, "level": 4, "name": "设备调度主管"}, {"code": 6666601027, "positionType": 4, "level": 4, "name": "运营经理"}], "positionType": 3, "level": 3, "name": "运营经理/主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130402, "subLevelModelList": [{"code": 6666600053, "positionType": 4, "level": 4, "name": "运营总监"}, {"code": 6666600125, "positionType": 4, "level": 4, "name": "医疗健康机构运营总监"}, {"code": 6666600505, "positionType": 4, "level": 4, "name": "药房运营总监"}, {"code": 6666600516, "positionType": 4, "level": 4, "name": "餐饮品牌运营总监"}, {"code": 6666600551, "positionType": 4, "level": 4, "name": "夜店娱乐运营总监"}], "positionType": 3, "level": 3, "name": "运营总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130403, "positionType": 3, "level": 3, "name": "COO", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130404, "subLevelModelList": [{"code": 6666600807, "positionType": 4, "level": 4, "name": "客服总监"}], "positionType": 3, "level": 3, "name": "客服总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "高端运营职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000290, "subLevelModelList": [{"code": 130501, "subLevelModelList": [{"code": 6666600782, "positionType": 4, "level": 4, "name": "教育运营专员"}, {"code": 6666600789, "positionType": 4, "level": 4, "name": "教育 NFT 数字藏品运营"}, {"code": 6666600947, "positionType": 4, "level": 4, "name": "绿色观赏植物销售/租赁专员"}, {"code": 6666600950, "positionType": 4, "level": 4, "name": "产品销售与交易服务"}, {"code": 6666600959, "positionType": 4, "level": 4, "name": "汽车零配件销售代表"}, {"code": 6666600980, "positionType": 4, "level": 4, "name": "销售渠道运营助理"}], "positionType": 3, "level": 3, "name": "其他运营职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他运营职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "客服/运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1070000, "subLevelModelList": [{"code": 1000490, "subLevelModelList": [{"code": 140301, "subLevelModelList": [{"code": 6666600003, "positionType": 4, "level": 4, "name": "五金交电销售专员"}, {"code": 6666600012, "positionType": 4, "level": 4, "name": "市场专员"}, {"code": 6666600030, "positionType": 4, "level": 4, "name": "租赁业务员"}, {"code": 6666600044, "positionType": 4, "level": 4, "name": "销售"}, {"code": 6666600051, "positionType": 4, "level": 4, "name": "暖通产品销售专员"}, {"code": 6666600057, "positionType": 4, "level": 4, "name": "销售专员"}, {"code": 6666600068, "positionType": 4, "level": 4, "name": "建筑材料销售岗位"}, {"code": 6666600077, "positionType": 4, "level": 4, "name": "教具销售代表"}, {"code": 6666600089, "positionType": 4, "level": 4, "name": "建筑材料销售代表"}, {"code": 6666600094, "positionType": 4, "level": 4, "name": "果蔬产品销售专员"}, {"code": 6666600098, "positionType": 4, "level": 4, "name": "导购员"}, {"code": 6666600099, "positionType": 4, "level": 4, "name": "皮鞋销售顾问"}, {"code": 6666600112, "positionType": 4, "level": 4, "name": "教学仪器销售专员"}, {"code": 6666600130, "positionType": 4, "level": 4, "name": "销售市场推广专员"}, {"code": 6666600151, "positionType": 4, "level": 4, "name": "建筑销售顾问"}, {"code": 6666600160, "positionType": 4, "level": 4, "name": "建筑劳务销售专员"}, {"code": 6666600180, "positionType": 4, "level": 4, "name": "便利店渠道销售专员（即食食品 + 调味小包）"}, {"code": 6666600186, "positionType": 4, "level": 4, "name": "快消品塑料销售专员"}, {"code": 6666600211, "positionType": 4, "level": 4, "name": "建筑销售市场调研专员"}, {"code": 6666600226, "positionType": 4, "level": 4, "name": "茶叶种植与销售专员"}, {"code": 6666600251, "positionType": 4, "level": 4, "name": "家具销售专员"}, {"code": 6666600264, "positionType": 4, "level": 4, "name": "化工产品市场专员"}, {"code": 6666600326, "positionType": 4, "level": 4, "name": "冻品销售专员"}, {"code": 6666600387, "positionType": 4, "level": 4, "name": "建筑销售渠道拓展专员"}, {"code": 6666600388, "positionType": 4, "level": 4, "name": "调味品渠道销售专员"}, {"code": 6666600391, "positionType": 4, "level": 4, "name": "租赁业务专员"}, {"code": 6666600435, "positionType": 4, "level": 4, "name": "建筑材料区域销售代表"}, {"code": 6666600460, "positionType": 4, "level": 4, "name": "建筑材料销售专员"}, {"code": 6666600463, "positionType": 4, "level": 4, "name": "工程设备销售专员"}, {"code": 6666600499, "positionType": 4, "level": 4, "name": "种植销售专员"}, {"code": 6666600528, "positionType": 4, "level": 4, "name": "销售员"}, {"code": 6666600578, "positionType": 4, "level": 4, "name": "金属矿石销售专员"}, {"code": 6666600580, "positionType": 4, "level": 4, "name": "水产养殖设备销售工程师"}, {"code": 6666600589, "positionType": 4, "level": 4, "name": "酒类营销专员（白酒 / 葡萄酒方向）"}, {"code": 6666600599, "positionType": 4, "level": 4, "name": "客户销售代表（企业团购）"}, {"code": 6666600629, "positionType": 4, "level": 4, "name": "农产品销售专员"}, {"code": 6666600632, "positionType": 4, "level": 4, "name": "照明器具销售"}, {"code": 6666600652, "positionType": 4, "level": 4, "name": "塑料加工销售专员"}, {"code": 6666600660, "positionType": 4, "level": 4, "name": "房产信息咨询顾问"}, {"code": 6666600661, "positionType": 4, "level": 4, "name": "农业机械租赁公司销售顾问"}, {"code": 6666600671, "positionType": 4, "level": 4, "name": "消防器材销售岗位"}, {"code": 6666600737, "positionType": 4, "level": 4, "name": "楼盘销售代理"}, {"code": 6666600745, "positionType": 4, "level": 4, "name": "生猪养殖销售专员"}, {"code": 6666600749, "positionType": 4, "level": 4, "name": "药品零售店员"}, {"code": 6666600775, "positionType": 4, "level": 4, "name": "销售项目助理"}, {"code": 6666600827, "positionType": 4, "level": 4, "name": "农副产品销售"}, {"code": 6666600835, "positionType": 4, "level": 4, "name": "农产品批发销售专员"}, {"code": 6666600855, "positionType": 4, "level": 4, "name": "农副产品互联网销售专员"}, {"code": 6666600867, "positionType": 4, "level": 4, "name": "电子产品销售经理"}, {"code": 6666600903, "positionType": 4, "level": 4, "name": "门窗销售顾问"}, {"code": 6666600934, "positionType": 4, "level": 4, "name": "销售业务拓展专员"}, {"code": 6666600988, "positionType": 4, "level": 4, "name": "销售客户开发专员"}, {"code": 6666601037, "positionType": 4, "level": 4, "name": "销售代表"}, {"code": 6666601080, "positionType": 4, "level": 4, "name": "塑料模具销售代表"}], "positionType": 3, "level": 3, "name": "销售专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140310, "positionType": 3, "level": 3, "name": "电话销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140314, "subLevelModelList": [{"code": 6666600131, "positionType": 4, "level": 4, "name": "销售网络营销专员"}, {"code": 6666600897, "positionType": 4, "level": 4, "name": "销售网络营销经理"}], "positionType": 3, "level": 3, "name": "网络销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140307, "subLevelModelList": [{"code": 6666600239, "positionType": 4, "level": 4, "name": "渠道开发经理"}, {"code": 6666600244, "positionType": 4, "level": 4, "name": "渠道销售主管（农产品批发市场）"}, {"code": 6666600252, "positionType": 4, "level": 4, "name": "销售项目协调员"}, {"code": 6666600272, "positionType": 4, "level": 4, "name": "销售渠道专员"}, {"code": 6666600301, "positionType": 4, "level": 4, "name": "销售 IP 合作专员"}, {"code": 6666600311, "positionType": 4, "level": 4, "name": "渠道专员"}, {"code": 6666600338, "positionType": 4, "level": 4, "name": "起重机械租赁顾问"}, {"code": 6666600397, "positionType": 4, "level": 4, "name": "销售渠道运营专员"}, {"code": 6666600418, "positionType": 4, "level": 4, "name": "场景化租赁业务经理"}, {"code": 6666601010, "positionType": 4, "level": 4, "name": "建材销售经理"}], "positionType": 3, "level": 3, "name": "渠道销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140304, "subLevelModelList": [{"code": 6666600032, "positionType": 4, "level": 4, "name": "大客户销售经理"}, {"code": 6666600067, "positionType": 4, "level": 4, "name": "五金产品渠道经理"}, {"code": 6666600113, "positionType": 4, "level": 4, "name": "大客户服务经理"}, {"code": 6666600181, "positionType": 4, "level": 4, "name": "大客户销售代表（商超 / 餐饮）"}, {"code": 6666600405, "positionType": 4, "level": 4, "name": "高空作业设备租赁专员"}, {"code": 6666600513, "positionType": 4, "level": 4, "name": "橡胶制品大客户经理"}, {"code": 6666600553, "positionType": 4, "level": 4, "name": "客户服务代表"}, {"code": 6666600804, "positionType": 4, "level": 4, "name": "机械零件大客户专员"}, {"code": 6666600868, "positionType": 4, "level": 4, "name": "商超渠道销售代表（食品 + 调味品）"}, {"code": 6666600871, "positionType": 4, "level": 4, "name": "计算机销售代表岗位"}], "positionType": 3, "level": 3, "name": "大客户代表", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140317, "subLevelModelList": [{"code": 6666600050, "positionType": 4, "level": 4, "name": "办公用品销售主管"}, {"code": 6666600140, "positionType": 4, "level": 4, "name": "塑料原料渠道经理"}, {"code": 6666600164, "positionType": 4, "level": 4, "name": "桁架租赁业务经理"}, {"code": 6666600206, "positionType": 4, "level": 4, "name": "客户关系主管"}, {"code": 6666600279, "positionType": 4, "level": 4, "name": "公司客户经理"}, {"code": 6666600317, "positionType": 4, "level": 4, "name": "客户服务经理"}, {"code": 6666600355, "positionType": 4, "level": 4, "name": "建筑销售客户服务专员"}, {"code": 6666600433, "positionType": 4, "level": 4, "name": "客户关系经理"}, {"code": 6666600466, "positionType": 4, "level": 4, "name": "塑料制品销售顾问 "}, {"code": 6666600467, "positionType": 4, "level": 4, "name": "食品销售客服专员"}], "positionType": 3, "level": 3, "name": "客户经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140305, "subLevelModelList": [{"code": 6666600015, "positionType": 4, "level": 4, "name": "调味品渠道经理（华南 / 西南）"}, {"code": 6666600063, "positionType": 4, "level": 4, "name": "校企合作经理"}, {"code": 6666600351, "positionType": 4, "level": 4, "name": "销售品牌传播专员"}, {"code": 6666600431, "positionType": 4, "level": 4, "name": "销售代理"}], "positionType": 3, "level": 3, "name": "BD经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140316, "subLevelModelList": [{"code": 6666600146, "positionType": 4, "level": 4, "name": "模具销售工程师"}, {"code": 6666600394, "positionType": 4, "level": 4, "name": "销售工程师（设备类）"}, {"code": 6666600470, "positionType": 4, "level": 4, "name": "租赁方案设计师"}, {"code": 6666600478, "positionType": 4, "level": 4, "name": "发电机销售工程师"}, {"code": 6666600577, "positionType": 4, "level": 4, "name": "机械设备售后客服工程师"}, {"code": 6666600805, "positionType": 4, "level": 4, "name": "工业塑料件销售工程师"}, {"code": 6666600851, "positionType": 4, "level": 4, "name": "销售工程师（国内）"}, {"code": 6666601013, "positionType": 4, "level": 4, "name": "销售工程师（软件类）"}], "positionType": 3, "level": 3, "name": "销售工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000520, "subLevelModelList": [{"code": 140302, "subLevelModelList": [{"code": 6666600104, "positionType": 4, "level": 4, "name": "销售经理​"}, {"code": 6666600121, "positionType": 4, "level": 4, "name": "区域销售主管（机械设备）"}, {"code": 6666600148, "positionType": 4, "level": 4, "name": "食品互联网销售经理"}, {"code": 6666600175, "positionType": 4, "level": 4, "name": "销售经理（金属材料）"}, {"code": 6666600178, "positionType": 4, "level": 4, "name": "销售渠道管理经理"}, {"code": 6666600190, "positionType": 4, "level": 4, "name": "酒类渠道销售经理"}, {"code": 6666600249, "positionType": 4, "level": 4, "name": "五金交电销售经理"}, {"code": 6666600258, "positionType": 4, "level": 4, "name": "跨境租赁业务经理（涉外项目）"}, {"code": 6666600261, "positionType": 4, "level": 4, "name": "工程设备销售经理"}, {"code": 6666600280, "positionType": 4, "level": 4, "name": "建筑材料销售经理"}, {"code": 6666600359, "positionType": 4, "level": 4, "name": "电线电缆销售主管"}, {"code": 6666600366, "positionType": 4, "level": 4, "name": "电线电缆渠道经理"}, {"code": 6666600385, "positionType": 4, "level": 4, "name": "建筑机械租赁海外业务经理"}, {"code": 6666600454, "positionType": 4, "level": 4, "name": "销售客户开发主管"}, {"code": 6666600487, "positionType": 4, "level": 4, "name": "劳保用品销售经理"}, {"code": 6666600496, "positionType": 4, "level": 4, "name": "混凝土设备租赁主管"}, {"code": 6666600504, "positionType": 4, "level": 4, "name": "销售经理（摩托车配件/工业设备/机电设备）"}, {"code": 6666600585, "positionType": 4, "level": 4, "name": "通信设备销售经理"}, {"code": 6666600591, "positionType": 4, "level": 4, "name": "塑料包装销售经理"}, {"code": 6666600595, "positionType": 4, "level": 4, "name": " 销售内勤主管"}, {"code": 6666600596, "positionType": 4, "level": 4, "name": "汽摩配件销售经理"}, {"code": 6666600614, "positionType": 4, "level": 4, "name": "销售主管"}, {"code": 6666600627, "positionType": 4, "level": 4, "name": "销售内勤主管"}, {"code": 6666600700, "positionType": 4, "level": 4, "name": "金属材料销售经理"}, {"code": 6666600705, "positionType": 4, "level": 4, "name": "租赁业务拓展经理"}, {"code": 6666600740, "positionType": 4, "level": 4, "name": "销售客户服务主管"}, {"code": 6666600756, "positionType": 4, "level": 4, "name": "机械设备销售经理"}, {"code": 6666600762, "positionType": 4, "level": 4, "name": "销售渠道招商经理"}, {"code": 6666600778, "positionType": 4, "level": 4, "name": "销售经理（工程机械）"}, {"code": 6666600831, "positionType": 4, "level": 4, "name": "大客户销售经理（餐饮连锁）"}, {"code": 6666600856, "positionType": 4, "level": 4, "name": "销售经理（技术方向）"}, {"code": 6666600896, "positionType": 4, "level": 4, "name": "日用百货销售经理"}, {"code": 6666600942, "positionType": 4, "level": 4, "name": "销售经理"}, {"code": 6666600943, "positionType": 4, "level": 4, "name": "销售经理"}, {"code": 6666600945, "positionType": 4, "level": 4, "name": "食品便利店渠道主管"}, {"code": 6666600955, "positionType": 4, "level": 4, "name": "社区团购食品经理"}, {"code": 6666601011, "positionType": 4, "level": 4, "name": "五金零件销售经理"}, {"code": 6666601058, "positionType": 4, "level": 4, "name": "销售培训主管"}], "positionType": 3, "level": 3, "name": "销售经理/主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140402, "subLevelModelList": [{"code": 6666600034, "positionType": 4, "level": 4, "name": "食品调味品营销总监"}, {"code": 6666600047, "positionType": 4, "level": 4, "name": "市场营销总监"}, {"code": 6666600085, "positionType": 4, "level": 4, "name": " 汽摩配件销售总监"}, {"code": 6666600136, "positionType": 4, "level": 4, "name": "建筑装饰材料销售总监"}, {"code": 6666600319, "positionType": 4, "level": 4, "name": "销售总监​"}, {"code": 6666600372, "positionType": 4, "level": 4, "name": "食品销售总监（含调味品）"}, {"code": 6666600393, "positionType": 4, "level": 4, "name": "工程机械销售总监"}, {"code": 6666600417, "positionType": 4, "level": 4, "name": "办公用品销售总监"}, {"code": 6666600440, "positionType": 4, "level": 4, "name": "农副产品销售总监"}, {"code": 6666600515, "positionType": 4, "level": 4, "name": "建筑劳务销售总监"}, {"code": 6666600561, "positionType": 4, "level": 4, "name": "租赁业务总监"}, {"code": 6666600682, "positionType": 4, "level": 4, "name": "销售产品总监"}, {"code": 6666600697, "positionType": 4, "level": 4, "name": "全品类销售总监"}, {"code": 6666600735, "positionType": 4, "level": 4, "name": "暖通产品销售总监"}, {"code": 6666600765, "positionType": 4, "level": 4, "name": "销售客户关系总监"}, {"code": 6666601040, "positionType": 4, "level": 4, "name": "工业塑料制品大客户销售"}, {"code": 6666601050, "positionType": 4, "level": 4, "name": "销售总监"}], "positionType": 3, "level": 3, "name": "销售总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160103, "subLevelModelList": [{"code": 6666600245, "positionType": 4, "level": 4, "name": "产品销售"}, {"code": 6666600331, "positionType": 4, "level": 4, "name": "餐饮终端业务员（调味品）"}, {"code": 6666600332, "positionType": 4, "level": 4, "name": "仪器仪表技术销售工程师"}], "positionType": 3, "level": 3, "name": "销售VP", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160102, "subLevelModelList": [{"code": 6666600870, "positionType": 4, "level": 4, "name": "外贸销售经理"}], "positionType": 3, "level": 3, "name": "城市经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160101, "subLevelModelList": [{"code": 6666600254, "positionType": 4, "level": 4, "name": "餐饮渠道调味专家"}, {"code": 6666600262, "positionType": 4, "level": 4, "name": "建筑材料区域销售主管"}, {"code": 6666600310, "positionType": 4, "level": 4, "name": "区域销售经理（商超渠道）"}, {"code": 6666600322, "positionType": 4, "level": 4, "name": "销售大区经理"}, {"code": 6666600361, "positionType": 4, "level": 4, "name": "区域经理"}, {"code": 6666600412, "positionType": 4, "level": 4, "name": "销售区域销售代表"}, {"code": 6666600558, "positionType": 4, "level": 4, "name": "区域销售经理（华北 / 华中）"}, {"code": 6666600858, "positionType": 4, "level": 4, "name": "区域销售经理（华北 / 华东）"}], "positionType": 3, "level": 3, "name": "区域总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "销售管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000420, "subLevelModelList": [{"code": 140309, "subLevelModelList": [{"code": 6666600132, "positionType": 4, "level": 4, "name": "销售业务拓展助理"}, {"code": 6666600699, "positionType": 4, "level": 4, "name": "销售客户开发助理"}, {"code": 6666600800, "positionType": 4, "level": 4, "name": "销售跨区域协同专员"}, {"code": 6666600802, "positionType": 4, "level": 4, "name": "项目助理"}, {"code": 6666600869, "positionType": 4, "level": 4, "name": "销售数据分析助理"}, {"code": 6666601023, "positionType": 4, "level": 4, "name": "销售客户关系管理助理"}], "positionType": 3, "level": 3, "name": "销售助理", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130119, "subLevelModelList": [{"code": 6666600651, "positionType": 4, "level": 4, "name": "销售风险管理专员"}, {"code": 6666601087, "positionType": 4, "level": 4, "name": "社群销售运营专员"}], "positionType": 3, "level": 3, "name": "销售运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160301, "subLevelModelList": [{"code": 6666600027, "positionType": 4, "level": 4, "name": "租赁报价专员"}, {"code": 6666600443, "positionType": 4, "level": 4, "name": "商务内勤助理"}, {"code": 6666601003, "positionType": 4, "level": 4, "name": "销售商务专员"}], "positionType": 3, "level": 3, "name": "商务专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140107, "subLevelModelList": [{"code": 6666600242, "positionType": 4, "level": 4, "name": "境外工程商务经理"}, {"code": 6666600250, "positionType": 4, "level": 4, "name": "租赁客户关系经理"}, {"code": 6666600260, "positionType": 4, "level": 4, "name": "销售商务经理"}, {"code": 6666600266, "positionType": 4, "level": 4, "name": "商务助理"}, {"code": 6666600349, "positionType": 4, "level": 4, "name": "商务经理"}], "positionType": 3, "level": 3, "name": "商务经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140403, "positionType": 3, "level": 3, "name": "商务总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160304, "subLevelModelList": [{"code": 6666601074, "positionType": 4, "level": 4, "name": "销售渠道招商专员"}], "positionType": 3, "level": 3, "name": "招商", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160303, "subLevelModelList": [{"code": 6666600048, "positionType": 4, "level": 4, "name": "建筑机械租赁客服经理"}, {"code": 6666600059, "positionType": 4, "level": 4, "name": "模架设备租赁经理"}, {"code": 6666600071, "positionType": 4, "level": 4, "name": "租赁销售经理"}, {"code": 6666600102, "positionType": 4, "level": 4, "name": "建筑工程机械与设备租赁"}, {"code": 6666600149, "positionType": 4, "level": 4, "name": "建筑工程机械租赁专员"}, {"code": 6666600184, "positionType": 4, "level": 4, "name": "土方机械租赁专员"}, {"code": 6666600362, "positionType": 4, "level": 4, "name": "销售客户成功经理"}, {"code": 6666600411, "positionType": 4, "level": 4, "name": "客户成功顾问"}], "positionType": 3, "level": 3, "name": "客户成功", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "销售行政/商务", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000480, "subLevelModelList": [{"code": 250203, "subLevelModelList": [{"code": 6666600481, "positionType": 4, "level": 4, "name": "外贸业务员"}], "positionType": 3, "level": 3, "name": "外贸业务员", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250201, "subLevelModelList": [{"code": 6666600448, "positionType": 4, "level": 4, "name": "五金外贸经理"}], "positionType": 3, "level": 3, "name": "外贸经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240111, "positionType": 3, "level": 3, "name": "货代/物流销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250205, "positionType": 3, "level": 3, "name": "海外销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "外贸销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000500, "subLevelModelList": [{"code": 190601, "subLevelModelList": [{"code": 6666600238, "positionType": 4, "level": 4, "name": "课程咨询师"}, {"code": 6666600288, "positionType": 4, "level": 4, "name": "课程顾问 "}, {"code": 6666600436, "positionType": 4, "level": 4, "name": "销售产品培训顾问"}, {"code": 6666600491, "positionType": 4, "level": 4, "name": "教育咨询顾问"}, {"code": 6666600937, "positionType": 4, "level": 4, "name": "课程销售顾问"}, {"code": 6666600958, "positionType": 4, "level": 4, "name": "教育老龄教育课程顾问"}], "positionType": 3, "level": 3, "name": "课程顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190603, "subLevelModelList": [{"code": 6666600484, "positionType": 4, "level": 4, "name": "留学规划师"}], "positionType": 3, "level": 3, "name": "留学顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "教培销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000450, "subLevelModelList": [{"code": 230201, "subLevelModelList": [{"code": 6666600119, "positionType": 4, "level": 4, "name": "二手车销售顾问"}, {"code": 6666600313, "positionType": 4, "level": 4, "name": "新能源汽车销售经理"}, {"code": 6666600398, "positionType": 4, "level": 4, "name": "汽车销售代表"}, {"code": 6666600509, "positionType": 4, "level": 4, "name": "汽车销售经理"}, {"code": 6666600788, "positionType": 4, "level": 4, "name": "汽车销售顾问"}, {"code": 6666600973, "positionType": 4, "level": 4, "name": "汽车销售"}, {"code": 6666601067, "positionType": 4, "level": 4, "name": "汽车零配件销售主管"}, {"code": 6666601100, "positionType": 4, "level": 4, "name": "新能源汽车整车销售顾问"}], "positionType": 3, "level": 3, "name": "汽车销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230202, "subLevelModelList": [{"code": 6666600292, "positionType": 4, "level": 4, "name": "汽车零配件零售顾问"}], "positionType": 3, "level": 3, "name": "汽车配件销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "汽车销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000430, "subLevelModelList": [{"code": 160401, "subLevelModelList": [{"code": 6666600552, "positionType": 4, "level": 4, "name": "业务顾问"}, {"code": 6666600813, "positionType": 4, "level": 4, "name": "销售业务发展总监"}, {"code": 6666600953, "positionType": 4, "level": 4, "name": "房地产营销策划经理"}, {"code": 6666600999, "positionType": 4, "level": 4, "name": "二手房买卖顾问"}, {"code": 6666601095, "positionType": 4, "level": 4, "name": "房地产经纪咨询顾问"}], "positionType": 3, "level": 3, "name": "置业顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160403, "subLevelModelList": [{"code": 6666600486, "positionType": 4, "level": 4, "name": "住房租赁专员"}, {"code": 6666600519, "positionType": 4, "level": 4, "name": "房屋租赁经纪人"}, {"code": 6666600562, "positionType": 4, "level": 4, "name": "房地产经纪人"}, {"code": 6666601036, "positionType": 4, "level": 4, "name": "租赁业务拓展专员"}, {"code": 6666601076, "positionType": 4, "level": 4, "name": "房屋租赁专员"}], "positionType": 3, "level": 3, "name": "地产中介", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220403, "subLevelModelList": [{"code": 6666600465, "positionType": 4, "level": 4, "name": "房地产营销咨询经理"}], "positionType": 3, "level": 3, "name": "地产招商", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "房地产销售/招商", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000440, "subLevelModelList": [{"code": 290302, "subLevelModelList": [{"code": 6666600638, "positionType": 4, "level": 4, "name": "鞋帽百货销售顾问/导购"}, {"code": 6666600946, "positionType": 4, "level": 4, "name": "处方药品导购专员"}], "positionType": 3, "level": 3, "name": "导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160501, "positionType": 3, "level": 3, "name": "服装导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290312, "positionType": 3, "level": 3, "name": "珠宝销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210414, "positionType": 3, "level": 3, "name": "美容顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210406, "positionType": 3, "level": 3, "name": "化妆品导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160502, "positionType": 3, "level": 3, "name": "家装导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210610, "positionType": 3, "level": 3, "name": "会籍顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280103, "positionType": 3, "level": 3, "name": "旅游顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "服务业销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000510, "subLevelModelList": [{"code": 210502, "positionType": 3, "level": 3, "name": "医药代表", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210506, "subLevelModelList": [{"code": 6666600177, "positionType": 4, "level": 4, "name": "医疗器械租赁专员"}, {"code": 6666600473, "positionType": 4, "level": 4, "name": "医疗器械销售总监"}, {"code": 6666600480, "positionType": 4, "level": 4, "name": "医疗器械销售经理（三类器械方向）"}], "positionType": 3, "level": 3, "name": "医疗器械销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210803, "positionType": 3, "level": 3, "name": "药店店员", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210801, "subLevelModelList": [{"code": 6666600061, "positionType": 4, "level": 4, "name": "药品零售门店店长"}], "positionType": 3, "level": 3, "name": "药店店长", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210505, "subLevelModelList": [{"code": 6666600348, "positionType": 4, "level": 4, "name": "美容产品销售顾问"}], "positionType": 3, "level": 3, "name": "医美咨询", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210504, "subLevelModelList": [{"code": 6666600007, "positionType": 4, "level": 4, "name": "防腐保温材料技术顾问"}, {"code": 6666600479, "positionType": 4, "level": 4, "name": "健康咨询顾问"}, {"code": 6666600875, "positionType": 4, "level": 4, "name": "药品销售经理"}, {"code": 6666600876, "positionType": 4, "level": 4, "name": "餐饮娱乐合规顾问"}], "positionType": 3, "level": 3, "name": "健康顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210507, "positionType": 3, "level": 3, "name": "口腔咨询师", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "医疗销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000460, "subLevelModelList": [{"code": 140313, "subLevelModelList": [{"code": 6666600010, "positionType": 4, "level": 4, "name": "智能建筑系统销售代表"}], "positionType": 3, "level": 3, "name": "广告销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140504, "subLevelModelList": [{"code": 6666600247, "positionType": 4, "level": 4, "name": "会展销售顾问"}, {"code": 6666600888, "positionType": 4, "level": 4, "name": "会议及展览服务专员"}], "positionType": 3, "level": 3, "name": "会展活动销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140501, "positionType": 3, "level": 3, "name": "会议活动销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "广告/会展销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000470, "subLevelModelList": [{"code": 180506, "subLevelModelList": [{"code": 6666601030, "positionType": 4, "level": 4, "name": "销售渠道拓展主管"}], "positionType": 3, "level": 3, "name": "理财顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 180701, "subLevelModelList": [{"code": 6666600150, "positionType": 4, "level": 4, "name": "租赁设备保险对接员"}], "positionType": 3, "level": 3, "name": "保险顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 180401, "positionType": 3, "level": 3, "name": "信用卡销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 180801, "positionType": 3, "level": 3, "name": "证券经纪人", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "金融销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000530, "subLevelModelList": [{"code": 160201, "subLevelModelList": [{"code": 6666600608, "positionType": 4, "level": 4, "name": "化工产品销售代表"}, {"code": 6666600844, "positionType": 4, "level": 4, "name": "五金产品零售岗位"}, {"code": 6666600847, "positionType": 4, "level": 4, "name": "建筑材料销售"}, {"code": 6666600881, "positionType": 4, "level": 4, "name": "建筑工程保险销售经理"}, {"code": 6666600882, "positionType": 4, "level": 4, "name": "大客户经理（汽车零配件）"}, {"code": 6666600900, "positionType": 4, "level": 4, "name": "机械配件销售工程师（轴承 / 齿轮）"}, {"code": 6666600911, "positionType": 4, "level": 4, "name": "技术销售工程师（传动部件）"}, {"code": 6666600921, "positionType": 4, "level": 4, "name": "再生资源销售"}, {"code": 6666600926, "positionType": 4, "level": 4, "name": "销售客户服务专员（线上）"}, {"code": 6666600940, "positionType": 4, "level": 4, "name": "乐器销售顾问"}], "positionType": 3, "level": 3, "name": "其他销售职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他销售职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1060000, "subLevelModelList": [{"code": 1000370, "subLevelModelList": [{"code": 150104, "subLevelModelList": [{"code": 6666600346, "positionType": 4, "level": 4, "name": "食品人力资源专员"}, {"code": 6666600502, "positionType": 4, "level": 4, "name": "劳务管理员"}, {"code": 6666600834, "positionType": 4, "level": 4, "name": "人力资源专员（农业公司）"}, {"code": 6666601004, "positionType": 4, "level": 4, "name": "人力资源专员"}], "positionType": 3, "level": 3, "name": "人力资源专员/助理", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150403, "positionType": 3, "level": 3, "name": "人力资源经理/主管", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150108, "subLevelModelList": [{"code": 6666600316, "positionType": 4, "level": 4, "name": "人力资源总监"}], "positionType": 3, "level": 3, "name": "人力资源总监", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150102, "subLevelModelList": [{"code": 6666600065, "positionType": 4, "level": 4, "name": " 招聘专员 "}, {"code": 6666600087, "positionType": 4, "level": 4, "name": "招聘专员"}], "positionType": 3, "level": 3, "name": "招聘", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150103, "positionType": 3, "level": 3, "name": "HRBP", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150105, "positionType": 3, "level": 3, "name": "培训", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150109, "positionType": 3, "level": 3, "name": "员工关系", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150110, "positionType": 3, "level": 3, "name": "组织发展", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150111, "subLevelModelList": [{"code": 6666600899, "positionType": 4, "level": 4, "name": "企业文化专员"}], "positionType": 3, "level": 3, "name": "企业文化", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150106, "positionType": 3, "level": 3, "name": "薪酬绩效", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 260108, "subLevelModelList": [{"code": 6666600423, "positionType": 4, "level": 4, "name": "教育行业猎头顾问"}, {"code": 6666600630, "positionType": 4, "level": 4, "name": "招聘代理（房地产行业猎头）"}], "positionType": 3, "level": 3, "name": "猎头顾问", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "人力资源", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu"}, {"code": 1000380, "subLevelModelList": [{"code": 150201, "subLevelModelList": [{"code": 6666600028, "positionType": 4, "level": 4, "name": "租赁设备资产管理专员"}, {"code": 6666600123, "positionType": 4, "level": 4, "name": "合同管理员"}, {"code": 6666600174, "positionType": 4, "level": 4, "name": "行政专员"}, {"code": 6666600286, "positionType": 4, "level": 4, "name": "租赁合同专员"}, {"code": 6666600452, "positionType": 4, "level": 4, "name": "行政助理"}], "positionType": 3, "level": 3, "name": "行政专员/助理", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150401, "subLevelModelList": [{"code": 6666600449, "positionType": 4, "level": 4, "name": "行政经理"}, {"code": 6666600694, "positionType": 4, "level": 4, "name": "行政主管"}], "positionType": 3, "level": 3, "name": "行政经理/主管", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150209, "subLevelModelList": [{"code": 6666600666, "positionType": 4, "level": 4, "name": "行政总监"}], "positionType": 3, "level": 3, "name": "行政总监", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150202, "subLevelModelList": [{"code": 6666600243, "positionType": 4, "level": 4, "name": "前台接待"}], "positionType": 3, "level": 3, "name": "前台", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150207, "subLevelModelList": [{"code": 6666600095, "positionType": 4, "level": 4, "name": "后勤主管"}, {"code": 6666600503, "positionType": 4, "level": 4, "name": "废旧物资回收专员"}], "positionType": 3, "level": 3, "name": "后勤", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150205, "subLevelModelList": [{"code": 6666600075, "positionType": 4, "level": 4, "name": "项目经理助理"}], "positionType": 3, "level": 3, "name": "经理助理", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150210, "subLevelModelList": [{"code": 6666600476, "positionType": 4, "level": 4, "name": "合约管理员"}, {"code": 6666601062, "positionType": 4, "level": 4, "name": "文员"}], "positionType": 3, "level": 3, "name": "文员", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 140802, "positionType": 3, "level": 3, "name": "企业党建", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150211, "subLevelModelList": [{"code": 6666600330, "positionType": 4, "level": 4, "name": "档案管理员"}, {"code": 6666600377, "positionType": 4, "level": 4, "name": "工程档案管理员"}], "positionType": 3, "level": 3, "name": "档案管理", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150208, "positionType": 3, "level": 3, "name": "商务司机", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 100603, "subLevelModelList": [{"code": 6666600024, "positionType": 4, "level": 4, "name": "咨询策划专员"}, {"code": 6666600314, "positionType": 4, "level": 4, "name": "信息管理专员"}, {"code": 6666600675, "positionType": 4, "level": 4, "name": "进出口业务专员"}, {"code": 6666600703, "positionType": 4, "level": 4, "name": "会议服务专员"}, {"code": 6666600722, "positionType": 4, "level": 4, "name": "租赁风控专员"}, {"code": 6666600832, "positionType": 4, "level": 4, "name": "项目主管"}, {"code": 6666600886, "positionType": 4, "level": 4, "name": "服务合规专员"}, {"code": 6666600917, "positionType": 4, "level": 4, "name": "物流项目专员"}, {"code": 6666600949, "positionType": 4, "level": 4, "name": "工会专员"}], "positionType": 3, "level": 3, "name": "项目专员/助理", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "行政", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu"}, {"code": 1000400, "subLevelModelList": [{"code": 150203, "subLevelModelList": [{"code": 6666600643, "positionType": 4, "level": 4, "name": "法务专员"}, {"code": 6666601077, "positionType": 4, "level": 4, "name": "合同专员"}], "positionType": 3, "level": 3, "name": "法务专员/助理", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150506, "positionType": 3, "level": 3, "name": "法务经理/主管", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150507, "subLevelModelList": [{"code": 6666600263, "positionType": 4, "level": 4, "name": "法务总监"}], "positionType": 3, "level": 3, "name": "法务总监", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 150504, "subLevelModelList": [{"code": 6666600966, "positionType": 4, "level": 4, "name": "工程法律顾问"}], "positionType": 3, "level": 3, "name": "法律顾问", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}, {"code": 260201, "positionType": 3, "level": 3, "name": "律师", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "法律服务", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu"}, {"code": 1000410, "subLevelModelList": [{"code": 150601, "subLevelModelList": [{"code": 6666600837, "positionType": 4, "level": 4, "name": "市政质量员"}, {"code": 6666600884, "positionType": 4, "level": 4, "name": "水产加工研发工程师"}, {"code": 6666600915, "positionType": 4, "level": 4, "name": "市场调查分析师"}, {"code": 6666601014, "positionType": 4, "level": 4, "name": "起重机械安装工艺设计师"}, {"code": 6666601015, "positionType": 4, "level": 4, "name": "技术支持工程师​"}, {"code": 6666601033, "positionType": 4, "level": 4, "name": "租赁设备巡检工程师"}], "positionType": 3, "level": 3, "name": "其他职能职位", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他职能职位", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu"}], "positionType": 0, "level": 1, "name": "人力/行政/法务", "searchKey": "re<PERSON><PERSON><PERSON><PERSON>g<PERSON>wu"}, {"code": 1230000, "subLevelModelList": [{"code": 1001250, "subLevelModelList": [{"code": 150301, "subLevelModelList": [{"code": 6666600645, "positionType": 4, "level": 4, "name": "财务会计"}, {"code": 6666600774, "positionType": 4, "level": 4, "name": "财务会计（农产品批发）"}, {"code": 6666600922, "positionType": 4, "level": 4, "name": "应收会计专员"}, {"code": 6666601073, "positionType": 4, "level": 4, "name": "会计"}], "positionType": 3, "level": 3, "name": "会计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150311, "positionType": 3, "level": 3, "name": "总账会计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150310, "subLevelModelList": [{"code": 6666600199, "positionType": 4, "level": 4, "name": "成本控制经理"}, {"code": 6666600526, "positionType": 4, "level": 4, "name": "项目成本会计"}, {"code": 6666600610, "positionType": 4, "level": 4, "name": "成本控制员"}, {"code": 6666601019, "positionType": 4, "level": 4, "name": "成本核算员"}], "positionType": 3, "level": 3, "name": "成本会计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150304, "subLevelModelList": [{"code": 6666600298, "positionType": 4, "level": 4, "name": "物流成本核算员"}, {"code": 6666600742, "positionType": 4, "level": 4, "name": "结算专员"}], "positionType": 3, "level": 3, "name": "结算会计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150313, "subLevelModelList": [{"code": 6666600269, "positionType": 4, "level": 4, "name": "税务会计"}], "positionType": 3, "level": 3, "name": "税务外勤会计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150312, "subLevelModelList": [{"code": 6666600142, "positionType": 4, "level": 4, "name": "成本控制工程师"}, {"code": 6666600430, "positionType": 4, "level": 4, "name": "工程成本控制专员"}, {"code": 6666601012, "positionType": 4, "level": 4, "name": "建筑培训课程销售经理"}], "positionType": 3, "level": 3, "name": "建筑/工程会计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "会计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001260, "subLevelModelList": [{"code": 150306, "subLevelModelList": [{"code": 6666600246, "positionType": 4, "level": 4, "name": "工程审计员"}, {"code": 6666601029, "positionType": 4, "level": 4, "name": "审计专员"}], "positionType": 3, "level": 3, "name": "审计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150305, "positionType": 3, "level": 3, "name": "税务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "审计/税务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001270, "subLevelModelList": [{"code": 150402, "subLevelModelList": [{"code": 6666600616, "positionType": 4, "level": 4, "name": "财务经理"}], "positionType": 3, "level": 3, "name": "财务经理/主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150308, "subLevelModelList": [{"code": 6666600036, "positionType": 4, "level": 4, "name": "成本总监"}, {"code": 6666600368, "positionType": 4, "level": 4, "name": "财务总监"}], "positionType": 3, "level": 3, "name": "财务总监/VP", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150404, "positionType": 3, "level": 3, "name": "CFO", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "高级财务职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000390, "subLevelModelList": [{"code": 150302, "subLevelModelList": [{"code": 6666600548, "positionType": 4, "level": 4, "name": "出纳"}], "positionType": 3, "level": 3, "name": "出纳", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150307, "subLevelModelList": [{"code": 6666600066, "positionType": 4, "level": 4, "name": "风险管理专员"}, {"code": 6666600422, "positionType": 4, "level": 4, "name": "租赁风控评估师"}, {"code": 6666600929, "positionType": 4, "level": 4, "name": "风控总监"}], "positionType": 3, "level": 3, "name": "风控", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150303, "subLevelModelList": [{"code": 6666601065, "positionType": 4, "level": 4, "name": "财务专员"}], "positionType": 3, "level": 3, "name": "财务顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150314, "subLevelModelList": [{"code": 6666600444, "positionType": 4, "level": 4, "name": "食品生产统计员"}], "positionType": 3, "level": 3, "name": "统计员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150316, "positionType": 3, "level": 3, "name": "财务分析/财务BP", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他财务岗位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "财务/审计/税务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1190000, "subLevelModelList": [{"code": 2600722, "subLevelModelList": [{"code": 300601, "subLevelModelList": [{"code": 6666600079, "positionType": 4, "level": 4, "name": "高空作业平台安装工"}, {"code": 6666600083, "positionType": 4, "level": 4, "name": "普工（杂工）"}, {"code": 6666600124, "positionType": 4, "level": 4, "name": "混凝土工"}, {"code": 6666600306, "positionType": 4, "level": 4, "name": "钢筋工"}, {"code": 6666600494, "positionType": 4, "level": 4, "name": "防水工"}, {"code": 6666600567, "positionType": 4, "level": 4, "name": "下水道工"}, {"code": 6666600568, "positionType": 4, "level": 4, "name": "污水处理工"}, {"code": 6666600669, "positionType": 4, "level": 4, "name": "玻璃安装工"}, {"code": 6666600709, "positionType": 4, "level": 4, "name": "五金产品生产操作工"}, {"code": 6666600718, "positionType": 4, "level": 4, "name": "燃气管道工"}, {"code": 6666600733, "positionType": 4, "level": 4, "name": "普工"}, {"code": 6666600905, "positionType": 4, "level": 4, "name": "道路养护工"}, {"code": 6666600925, "positionType": 4, "level": 4, "name": "管道工"}, {"code": 6666601016, "positionType": 4, "level": 4, "name": "架子工"}, {"code": 6666601032, "positionType": 4, "level": 4, "name": "燃气净化工"}, {"code": 6666601051, "positionType": 4, "level": 4, "name": "钢结构安装工"}], "positionType": 3, "level": 3, "name": "普工/操作工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300624, "subLevelModelList": [{"code": 6666600223, "positionType": 4, "level": 4, "name": "食品包装工（流水线）"}], "positionType": 3, "level": 3, "name": "包装工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300628, "positionType": 3, "level": 3, "name": "学徒工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300630, "positionType": 3, "level": 3, "name": "搬运工/装卸工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300623, "subLevelModelList": [{"code": 6666600188, "positionType": 4, "level": 4, "name": "舞台搭建工程师"}, {"code": 6666600224, "positionType": 4, "level": 4, "name": "安装质量员"}, {"code": 6666600265, "positionType": 4, "level": 4, "name": "管道安装工"}], "positionType": 3, "level": 3, "name": "组装工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "普工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600902, "subLevelModelList": [{"code": 300604, "subLevelModelList": [{"code": 6666600529, "positionType": 4, "level": 4, "name": "电焊工"}, {"code": 6666600874, "positionType": 4, "level": 4, "name": "机械零件加工技师"}], "positionType": 3, "level": 3, "name": "焊工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300605, "positionType": 3, "level": 3, "name": "氩弧焊工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300610, "subLevelModelList": [{"code": 6666600924, "positionType": 4, "level": 4, "name": "车间技术员（零件加工）"}], "positionType": 3, "level": 3, "name": "车工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300613, "subLevelModelList": [{"code": 6666600414, "positionType": 4, "level": 4, "name": "供水设备维修钳工"}], "positionType": 3, "level": 3, "name": "钳工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300631, "positionType": 3, "level": 3, "name": "切割工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300616, "positionType": 3, "level": 3, "name": "钣金工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300622, "positionType": 3, "level": 3, "name": "注塑工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300619, "positionType": 3, "level": 3, "name": "折弯工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300611, "positionType": 3, "level": 3, "name": "磨工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300633, "subLevelModelList": [{"code": 6666600625, "positionType": 4, "level": 4, "name": "模具项目主管"}, {"code": 6666600891, "positionType": 4, "level": 4, "name": "模具结构设计师"}, {"code": 6666600977, "positionType": 4, "level": 4, "name": "模具技术总监"}], "positionType": 3, "level": 3, "name": "模具工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300612, "positionType": 3, "level": 3, "name": "铣工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300617, "positionType": 3, "level": 3, "name": "抛光工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300621, "positionType": 3, "level": 3, "name": "喷塑工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300614, "positionType": 3, "level": 3, "name": "钻工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300615, "positionType": 3, "level": 3, "name": "铆工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300620, "positionType": 3, "level": 3, "name": "电镀工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "机械加工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001200, "subLevelModelList": [{"code": 300606, "subLevelModelList": [{"code": 6666601018, "positionType": 4, "level": 4, "name": "电工"}], "positionType": 3, "level": 3, "name": "电工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300635, "positionType": 3, "level": 3, "name": "弱电工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300618, "subLevelModelList": [{"code": 6666600073, "positionType": 4, "level": 4, "name": "设备维修机修工"}, {"code": 6666600276, "positionType": 4, "level": 4, "name": "机械维修技术员"}], "positionType": 3, "level": 3, "name": "机修工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300311, "subLevelModelList": [{"code": 6666600350, "positionType": 4, "level": 4, "name": "机械加工工艺工程师"}], "positionType": 3, "level": 3, "name": "CNC数控操机/编程员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300608, "subLevelModelList": [{"code": 6666600170, "positionType": 4, "level": 4, "name": "精细木工"}, {"code": 6666600507, "positionType": 4, "level": 4, "name": "木工"}, {"code": 6666601082, "positionType": 4, "level": 4, "name": "木工（模板工）"}], "positionType": 3, "level": 3, "name": "木工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300609, "subLevelModelList": [{"code": 6666600517, "positionType": 4, "level": 4, "name": "油漆工"}], "positionType": 3, "level": 3, "name": "油漆工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300627, "subLevelModelList": [{"code": 6666600054, "positionType": 4, "level": 4, "name": "锅炉操作工"}], "positionType": 3, "level": 3, "name": "锅炉工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "技工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600732, "subLevelModelList": [{"code": 300602, "subLevelModelList": [{"code": 6666600450, "positionType": 4, "level": 4, "name": "起重工"}, {"code": 6666600455, "positionType": 4, "level": 4, "name": "叉车操作员"}], "positionType": 3, "level": 3, "name": "叉车工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300603, "positionType": 3, "level": 3, "name": "铲车司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300634, "subLevelModelList": [{"code": 6666600879, "positionType": 4, "level": 4, "name": "挖掘机驾驶员"}], "positionType": 3, "level": 3, "name": "挖掘机司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "运输设备操作", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001130, "subLevelModelList": [{"code": 300201, "subLevelModelList": [{"code": 6666600021, "positionType": 4, "level": 4, "name": "质量管控经理"}, {"code": 6666600080, "positionType": 4, "level": 4, "name": "试验检测工程师"}, {"code": 6666600187, "positionType": 4, "level": 4, "name": "质量检验工程师"}, {"code": 6666600235, "positionType": 4, "level": 4, "name": "调味品质量管控主管"}, {"code": 6666600477, "positionType": 4, "level": 4, "name": "质量总监"}, {"code": 6666600794, "positionType": 4, "level": 4, "name": "防水工程师"}, {"code": 6666600822, "positionType": 4, "level": 4, "name": "设备维护工程师"}, {"code": 6666600846, "positionType": 4, "level": 4, "name": "设备租赁智能调度工程师"}, {"code": 6666600941, "positionType": 4, "level": 4, "name": "设备安装质量员"}, {"code": 6666600993, "positionType": 4, "level": 4, "name": "液压气动工程师"}, {"code": 6666601039, "positionType": 4, "level": 4, "name": "质量控制专员（QC）"}], "positionType": 3, "level": 3, "name": "质量管理/测试工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300208, "subLevelModelList": [{"code": 6666600344, "positionType": 4, "level": 4, "name": "食品包装材料检测员"}, {"code": 6666600424, "positionType": 4, "level": 4, "name": "质量检测员"}, {"code": 6666600458, "positionType": 4, "level": 4, "name": "水泥制品质量检验员"}, {"code": 6666600534, "positionType": 4, "level": 4, "name": "检验技术员"}, {"code": 6666600535, "positionType": 4, "level": 4, "name": "美容产品生产质检员"}, {"code": 6666600541, "positionType": 4, "level": 4, "name": "Ⅰ类医疗器械质检员"}, {"code": 6666600617, "positionType": 4, "level": 4, "name": "货运车辆检验员"}, {"code": 6666600673, "positionType": 4, "level": 4, "name": "质检员"}, {"code": 6666600685, "positionType": 4, "level": 4, "name": "土建质量员"}, {"code": 6666600719, "positionType": 4, "level": 4, "name": "质检工程师（金属零件）"}, {"code": 6666600883, "positionType": 4, "level": 4, "name": "机械设备维护员"}, {"code": 6666600919, "positionType": 4, "level": 4, "name": "质量控制专员"}, {"code": 6666600957, "positionType": 4, "level": 4, "name": "质检主管"}], "positionType": 3, "level": 3, "name": "质检员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300402, "subLevelModelList": [{"code": 6666600141, "positionType": 4, "level": 4, "name": "技术研究工程师"}, {"code": 6666600687, "positionType": 4, "level": 4, "name": "技术员"}, {"code": 6666600727, "positionType": 4, "level": 4, "name": "3D打印后处理技术员"}], "positionType": 3, "level": 3, "name": "实验室技术员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300205, "subLevelModelList": [{"code": 6666600109, "positionType": 4, "level": 4, "name": " 质量经理（IATF16949体系）"}, {"code": 6666600114, "positionType": 4, "level": 4, "name": "质量工程师（ISO 体系）"}, {"code": 6666600152, "positionType": 4, "level": 4, "name": "安全体系工程师"}, {"code": 6666600380, "positionType": 4, "level": 4, "name": "质量保证工程师（QA）"}, {"code": 6666600468, "positionType": 4, "level": 4, "name": "食品调味品资质专员"}, {"code": 6666600862, "positionType": 4, "level": 4, "name": "铝合金制品生产工程师"}], "positionType": 3, "level": 3, "name": "体系工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300206, "subLevelModelList": [{"code": 6666600615, "positionType": 4, "level": 4, "name": "认证审核员"}], "positionType": 3, "level": 3, "name": "体系审核员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300204, "positionType": 3, "level": 3, "name": "产品认证工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300203, "positionType": 3, "level": 3, "name": "失效分析工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300202, "positionType": 3, "level": 3, "name": "可靠度工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250108, "subLevelModelList": [{"code": 6666600383, "positionType": 4, "level": 4, "name": "质量工程师（模具 / 电子）"}, {"code": 6666600584, "positionType": 4, "level": 4, "name": "质量控制经理"}, {"code": 6666600649, "positionType": 4, "level": 4, "name": "技术支持工程师"}, {"code": 6666600849, "positionType": 4, "level": 4, "name": "质量工程师"}], "positionType": 3, "level": 3, "name": "供应商质量工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210122, "positionType": 3, "level": 3, "name": "医疗器械生产/质量管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230109, "positionType": 3, "level": 3, "name": "汽车质量工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300209, "positionType": 3, "level": 3, "name": "计量工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "质量管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001170, "subLevelModelList": [{"code": 300301, "subLevelModelList": [{"code": 6666600628, "positionType": 4, "level": 4, "name": "五金模具研发工程师"}, {"code": 6666600692, "positionType": 4, "level": 4, "name": "机械设计工程师"}, {"code": 6666601069, "positionType": 4, "level": 4, "name": "桥梁工程师"}], "positionType": 3, "level": 3, "name": "机械工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300306, "subLevelModelList": [{"code": 6666600205, "positionType": 4, "level": 4, "name": "五金产品结构设计师"}], "positionType": 3, "level": 3, "name": "机械结构工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300321, "positionType": 3, "level": 3, "name": "家电/3C结构工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300308, "subLevelModelList": [{"code": 6666600304, "positionType": 4, "level": 4, "name": "电子产品工艺工程师"}, {"code": 6666600379, "positionType": 4, "level": 4, "name": "工艺工程师（机加工）"}, {"code": 6666600447, "positionType": 4, "level": 4, "name": "工艺工程师"}, {"code": 6666600563, "positionType": 4, "level": 4, "name": "木材加工工艺师"}, {"code": 6666600607, "positionType": 4, "level": 4, "name": "塑料制品研发工程师"}, {"code": 6666600797, "positionType": 4, "level": 4, "name": "制造工艺工程师"}], "positionType": 3, "level": 3, "name": "工艺工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300304, "subLevelModelList": [{"code": 6666600070, "positionType": 4, "level": 4, "name": "售后服务工程师（设备维修）"}, {"code": 6666600093, "positionType": 4, "level": 4, "name": "通用设备维修技师"}, {"code": 6666600202, "positionType": 4, "level": 4, "name": "矿山机械维修技师"}, {"code": 6666600248, "positionType": 4, "level": 4, "name": "机具租赁设备维护专员"}, {"code": 6666600270, "positionType": 4, "level": 4, "name": "设备维护员"}, {"code": 6666600324, "positionType": 4, "level": 4, "name": "工程机械维修技师（液压系统方向）"}, {"code": 6666600428, "positionType": 4, "level": 4, "name": "机械设备维修工程师"}, {"code": 6666600605, "positionType": 4, "level": 4, "name": "维修技师（工程机械）"}, {"code": 6666600635, "positionType": 4, "level": 4, "name": "冷链设备维护工程师"}, {"code": 6666600785, "positionType": 4, "level": 4, "name": "3D打印设备维护工程师"}, {"code": 6666600792, "positionType": 4, "level": 4, "name": "租赁设备再制造规划师"}, {"code": 6666600823, "positionType": 4, "level": 4, "name": "设备调度中心主管"}, {"code": 6666601072, "positionType": 4, "level": 4, "name": "设备维修工程师"}], "positionType": 3, "level": 3, "name": "设备维修保养工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300303, "subLevelModelList": [{"code": 6666600274, "positionType": 4, "level": 4, "name": "机械制造工程师"}, {"code": 6666600439, "positionType": 4, "level": 4, "name": "设备健康监测工程师"}, {"code": 6666600619, "positionType": 4, "level": 4, "name": "工程机械电气维修工程师"}, {"code": 6666600783, "positionType": 4, "level": 4, "name": "租赁设备改装技师"}], "positionType": 3, "level": 3, "name": "机械设备工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300310, "subLevelModelList": [{"code": 6666600145, "positionType": 4, "level": 4, "name": "电机驱动工程师"}, {"code": 6666600848, "positionType": 4, "level": 4, "name": "机电安装工长"}, {"code": 6666601059, "positionType": 4, "level": 4, "name": "机电工程师"}], "positionType": 3, "level": 3, "name": "机电工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300305, "positionType": 3, "level": 3, "name": "机械制图员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300314, "subLevelModelList": [{"code": 6666600601, "positionType": 4, "level": 4, "name": "模具装配调试工程师"}, {"code": 6666601086, "positionType": 4, "level": 4, "name": "模具质量工程师"}], "positionType": 3, "level": 3, "name": "模具工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300313, "subLevelModelList": [{"code": 6666600830, "positionType": 4, "level": 4, "name": "工装夹具设计师"}], "positionType": 3, "level": 3, "name": "夹具工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300309, "subLevelModelList": [{"code": 6666600257, "positionType": 4, "level": 4, "name": "金属材料工程师"}, {"code": 6666600336, "positionType": 4, "level": 4, "name": "材料工程师"}, {"code": 6666600606, "positionType": 4, "level": 4, "name": "岩土工程师"}, {"code": 6666600838, "positionType": 4, "level": 4, "name": "装饰材料设计师"}], "positionType": 3, "level": 3, "name": "材料工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300307, "positionType": 3, "level": 3, "name": "工业工程师(IE)", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300319, "positionType": 3, "level": 3, "name": "仿真工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100813, "positionType": 3, "level": 3, "name": "热设计工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300316, "positionType": 3, "level": 3, "name": "注塑工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300315, "subLevelModelList": [{"code": 6666601093, "positionType": 4, "level": 4, "name": "焊接工程师"}], "positionType": 3, "level": 3, "name": "焊接工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300317, "positionType": 3, "level": 3, "name": "铸造/锻造工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300318, "subLevelModelList": [{"code": 6666600820, "positionType": 4, "level": 4, "name": "液压系统维修技师"}], "positionType": 3, "level": 3, "name": "液压工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300312, "subLevelModelList": [{"code": 6666600018, "positionType": 4, "level": 4, "name": "冲压模具工程师"}, {"code": 6666600360, "positionType": 4, "level": 4, "name": "冲压件品质主管"}], "positionType": 3, "level": 3, "name": "冲压工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300320, "subLevelModelList": [{"code": 6666600155, "positionType": 4, "level": 4, "name": "电气装配工"}, {"code": 6666600712, "positionType": 4, "level": 4, "name": "机械装配工"}], "positionType": 3, "level": 3, "name": "装配工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300802, "positionType": 3, "level": 3, "name": "电机工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "机械设计/制造", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600942, "subLevelModelList": [{"code": 101402, "subLevelModelList": [{"code": 6666600042, "positionType": 4, "level": 4, "name": "仪器仪表技术支持工程师"}, {"code": 6666600302, "positionType": 4, "level": 4, "name": "电气工程师"}, {"code": 6666600334, "positionType": 4, "level": 4, "name": "变配电运行工"}, {"code": 6666600564, "positionType": 4, "level": 4, "name": "电气产品销售工程师"}, {"code": 6666601006, "positionType": 4, "level": 4, "name": "电气控制工程师"}], "positionType": 3, "level": 3, "name": "电气工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101404, "subLevelModelList": [{"code": 6666600237, "positionType": 4, "level": 4, "name": "PCB 设计工程师"}, {"code": 6666600268, "positionType": 4, "level": 4, "name": "新能源设备研发工程师"}], "positionType": 3, "level": 3, "name": "电气设计工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100803, "subLevelModelList": [{"code": 6666600056, "positionType": 4, "level": 4, "name": "仪器仪表技术支持"}, {"code": 6666600547, "positionType": 4, "level": 4, "name": "数控编程工程师（模具）"}, {"code": 6666600824, "positionType": 4, "level": 4, "name": "自动化控制工程师"}], "positionType": 3, "level": 3, "name": "自动化工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "电气/自动化", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001120, "subLevelModelList": [{"code": 300103, "subLevelModelList": [{"code": 6666600225, "positionType": 4, "level": 4, "name": "机械加工车间主任"}, {"code": 6666600408, "positionType": 4, "level": 4, "name": "生产车间主任"}, {"code": 6666600560, "positionType": 4, "level": 4, "name": "生产经理"}, {"code": 6666600781, "positionType": 4, "level": 4, "name": "3D打印车间主管"}], "positionType": 3, "level": 3, "name": "车间主任", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300104, "subLevelModelList": [{"code": 6666600234, "positionType": 4, "level": 4, "name": "生产主管（精密机加工）"}, {"code": 6666600582, "positionType": 4, "level": 4, "name": "工长"}, {"code": 6666600644, "positionType": 4, "level": 4, "name": "食品生产经理（预包装"}, {"code": 6666600693, "positionType": 4, "level": 4, "name": "橡胶制品生产主管"}, {"code": 6666600759, "positionType": 4, "level": 4, "name": "钢铁生产主管"}, {"code": 6666600927, "positionType": 4, "level": 4, "name": "现场抢修组长"}, {"code": 6666601096, "positionType": 4, "level": 4, "name": "模板工长"}, {"code": 6666601102, "positionType": 4, "level": 4, "name": "摩托车配件生产主管"}], "positionType": 3, "level": 3, "name": "生产组长/拉长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300102, "subLevelModelList": [{"code": 6666601091, "positionType": 4, "level": 4, "name": "生产运营总监"}], "positionType": 3, "level": 3, "name": "生产总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300101, "subLevelModelList": [{"code": 6666600410, "positionType": 4, "level": 4, "name": "防水卷材生产厂长"}, {"code": 6666601001, "positionType": 4, "level": 4, "name": "五金制造厂长"}], "positionType": 3, "level": 3, "name": "厂长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300108, "positionType": 3, "level": 3, "name": "生产跟单/文员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300107, "subLevelModelList": [{"code": 6666600078, "positionType": 4, "level": 4, "name": "生产计划主管"}, {"code": 6666600192, "positionType": 4, "level": 4, "name": "生产计划员（电子产品）"}, {"code": 6666600575, "positionType": 4, "level": 4, "name": "3D打印设备操作员"}, {"code": 6666600690, "positionType": 4, "level": 4, "name": "3D打印生产计划员"}], "positionType": 3, "level": 3, "name": "生产计划/物料管理(PMC)", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300106, "subLevelModelList": [{"code": 6666600193, "positionType": 4, "level": 4, "name": "设备管理员"}, {"code": 6666600289, "positionType": 4, "level": 4, "name": "设备调度专员"}, {"code": 6666600364, "positionType": 4, "level": 4, "name": "物流成本分析师"}, {"code": 6666600399, "positionType": 4, "level": 4, "name": "设备租赁风险管理专员"}, {"code": 6666600524, "positionType": 4, "level": 4, "name": "维修车间主管"}, {"code": 6666600539, "positionType": 4, "level": 4, "name": "机械管理员"}, {"code": 6666600597, "positionType": 4, "level": 4, "name": "设备维护专员（工业机械）"}, {"code": 6666601066, "positionType": 4, "level": 4, "name": "设备调度员"}], "positionType": 3, "level": 3, "name": "生产设备管理员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300110, "positionType": 3, "level": 3, "name": "厂务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "生产营运", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600712, "subLevelModelList": [{"code": 300207, "subLevelModelList": [{"code": 6666600144, "positionType": 4, "level": 4, "name": "食品安全专员"}, {"code": 6666600600, "positionType": 4, "level": 4, "name": "安全员"}], "positionType": 3, "level": 3, "name": "生产安全员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300903, "subLevelModelList": [{"code": 6666600271, "positionType": 4, "level": 4, "name": "HSE主管（健康安全环保）"}, {"code": 6666600318, "positionType": 4, "level": 4, "name": "环保技术研发工程师"}, {"code": 6666600406, "positionType": 4, "level": 4, "name": "安全工程师"}], "positionType": 3, "level": 3, "name": "EHS工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300210, "subLevelModelList": [{"code": 6666600543, "positionType": 4, "level": 4, "name": "安全总监"}], "positionType": 3, "level": 3, "name": "安全评价师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "生产安全", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001180, "subLevelModelList": [{"code": 300402, "subLevelModelList": [{"code": 6666600141, "positionType": 4, "level": 4, "name": "技术研究工程师"}, {"code": 6666600687, "positionType": 4, "level": 4, "name": "技术员"}, {"code": 6666600727, "positionType": 4, "level": 4, "name": "3D打印后处理技术员"}], "positionType": 3, "level": 3, "name": "实验室技术员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300401, "positionType": 3, "level": 3, "name": "化工工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300406, "subLevelModelList": [{"code": 6666600255, "positionType": 4, "level": 4, "name": "速冻食品工艺员"}, {"code": 6666600296, "positionType": 4, "level": 4, "name": "食品行业研究员（政策 / 趋势）"}, {"code": 6666600483, "positionType": 4, "level": 4, "name": "食品工艺工程师（发酵食品）"}, {"code": 6666600525, "positionType": 4, "level": 4, "name": "食品研发工程师（功能食品）"}, {"code": 6666600592, "positionType": 4, "level": 4, "name": "食品出口单证员"}, {"code": 6666600624, "positionType": 4, "level": 4, "name": "食品销售数据录入员"}, {"code": 6666600715, "positionType": 4, "level": 4, "name": "食品仓储质量安全员"}, {"code": 6666600812, "positionType": 4, "level": 4, "name": "食品销售总监"}, {"code": 6666600818, "positionType": 4, "level": 4, "name": "食品合规专员（许可管理）"}], "positionType": 3, "level": 3, "name": "食品/饮料研发", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300405, "positionType": 3, "level": 3, "name": "化妆品研发", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300404, "positionType": 3, "level": 3, "name": "涂料研发", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300407, "positionType": 3, "level": 3, "name": "化工项目经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "化工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001190, "subLevelModelList": [{"code": 300510, "positionType": 3, "level": 3, "name": "服装/纺织/皮革跟单", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300509, "positionType": 3, "level": 3, "name": "打样/制版", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300501, "positionType": 3, "level": 3, "name": "服装/纺织设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 120615, "positionType": 3, "level": 3, "name": "鞋类设计师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300507, "positionType": 3, "level": 3, "name": "面料辅料开发", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300629, "positionType": 3, "level": 3, "name": "缝纫工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300632, "positionType": 3, "level": 3, "name": "样衣工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300511, "positionType": 3, "level": 3, "name": "量体师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300637, "positionType": 3, "level": 3, "name": "裁剪工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "服装/纺织/皮革", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001140, "subLevelModelList": [{"code": 300801, "positionType": 3, "level": 3, "name": "电池工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300802, "positionType": 3, "level": 3, "name": "电机工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "新能源汽车", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001150, "subLevelModelList": [{"code": 230102, "positionType": 3, "level": 3, "name": "车身/造型设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230106, "positionType": 3, "level": 3, "name": "汽车电子工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 101308, "subLevelModelList": [{"code": 6666601064, "positionType": 4, "level": 4, "name": "智能驾驶系统开发工程师"}], "positionType": 3, "level": 3, "name": "自动驾驶系统工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300803, "positionType": 3, "level": 3, "name": "线束设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230110, "subLevelModelList": [{"code": 6666600011, "positionType": 4, "level": 4, "name": "园林绿化工程师"}, {"code": 6666600760, "positionType": 4, "level": 4, "name": "设计工程师"}], "positionType": 3, "level": 3, "name": "内外饰设计工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230105, "positionType": 3, "level": 3, "name": "动力系统工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230103, "positionType": 3, "level": 3, "name": "底盘工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230107, "subLevelModelList": [{"code": 6666600017, "positionType": 4, "level": 4, "name": "汽车配件研发工程师"}], "positionType": 3, "level": 3, "name": "汽车零部件设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230101, "subLevelModelList": [{"code": 6666600008, "positionType": 4, "level": 4, "name": "汽车设计工程师"}], "positionType": 3, "level": 3, "name": "汽车设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230109, "positionType": 3, "level": 3, "name": "汽车质量工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230210, "positionType": 3, "level": 3, "name": "总装工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230108, "positionType": 3, "level": 3, "name": "汽车项目管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230111, "positionType": 3, "level": 3, "name": "总布置工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "汽车研发/制造", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600602, "subLevelModelList": [{"code": 300903, "subLevelModelList": [{"code": 6666600271, "positionType": 4, "level": 4, "name": "HSE主管（健康安全环保）"}, {"code": 6666600318, "positionType": 4, "level": 4, "name": "环保技术研发工程师"}, {"code": 6666600406, "positionType": 4, "level": 4, "name": "安全工程师"}], "positionType": 3, "level": 3, "name": "EHS工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300905, "subLevelModelList": [{"code": 6666600105, "positionType": 4, "level": 4, "name": "检测员"}, {"code": 6666600229, "positionType": 4, "level": 4, "name": "微生物检测员"}, {"code": 6666600659, "positionType": 4, "level": 4, "name": "预防性维护工程师"}, {"code": 6666600725, "positionType": 4, "level": 4, "name": "质量检测员​"}], "positionType": 3, "level": 3, "name": "环境采样/检测员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300902, "subLevelModelList": [{"code": 6666600723, "positionType": 4, "level": 4, "name": "质量体系工程师"}], "positionType": 3, "level": 3, "name": "环评工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300901, "subLevelModelList": [{"code": 6666600009, "positionType": 4, "level": 4, "name": "环保咨询顾问"}, {"code": 6666600165, "positionType": 4, "level": 4, "name": "环境工程师"}, {"code": 6666600176, "positionType": 4, "level": 4, "name": "环保项目商务经理"}, {"code": 6666600390, "positionType": 4, "level": 4, "name": "环保工程师"}, {"code": 6666600434, "positionType": 4, "level": 4, "name": "环境技术研发工程师"}, {"code": 6666600540, "positionType": 4, "level": 4, "name": "工程环保专员"}, {"code": 6666600728, "positionType": 4, "level": 4, "name": "再制造工程师"}], "positionType": 3, "level": 3, "name": "环保工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300904, "subLevelModelList": [{"code": 6666600472, "positionType": 4, "level": 4, "name": "销售碳中和策略专员"}, {"code": 6666601097, "positionType": 4, "level": 4, "name": "环保管理员"}], "positionType": 3, "level": 3, "name": "碳排放管理师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "环保", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001210, "subLevelModelList": [{"code": 300701, "subLevelModelList": [{"code": 6666600574, "positionType": 4, "level": 4, "name": "模具制造工程师"}, {"code": 6666600656, "positionType": 4, "level": 4, "name": "增材制造工艺师"}, {"code": 6666600662, "positionType": 4, "level": 4, "name": "冲压件生产主管"}, {"code": 6666600795, "positionType": 4, "level": 4, "name": "涂装工艺员"}, {"code": 6666600803, "positionType": 4, "level": 4, "name": "冶金技术研发工程师"}, {"code": 6666600808, "positionType": 4, "level": 4, "name": "绿色制造工程师"}, {"code": 6666601088, "positionType": 4, "level": 4, "name": "食品研发工程师"}, {"code": 6666601090, "positionType": 4, "level": 4, "name": "非标零件设计工程师"}], "positionType": 3, "level": 3, "name": "其他生产制造职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他生产制造职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "生产制造", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1180000, "subLevelModelList": [{"code": 1001050, "subLevelModelList": [{"code": 290303, "subLevelModelList": [{"code": 6666600392, "positionType": 4, "level": 4, "name": "五金产品零售"}], "positionType": 3, "level": 3, "name": "店员/营业员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290201, "positionType": 3, "level": 3, "name": "收银", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290302, "subLevelModelList": [{"code": 6666600638, "positionType": 4, "level": 4, "name": "鞋帽百货销售顾问/导购"}, {"code": 6666600946, "positionType": 4, "level": 4, "name": "处方药品导购专员"}], "positionType": 3, "level": 3, "name": "导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160501, "positionType": 3, "level": 3, "name": "服装导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290312, "positionType": 3, "level": 3, "name": "珠宝销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210406, "positionType": 3, "level": 3, "name": "化妆品导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160502, "positionType": 3, "level": 3, "name": "家装导购", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290307, "positionType": 3, "level": 3, "name": "理货员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290311, "positionType": 3, "level": 3, "name": "促销员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290306, "positionType": 3, "level": 3, "name": "陈列员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290308, "positionType": 3, "level": 3, "name": "防损员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290304, "subLevelModelList": [{"code": 6666600115, "positionType": 4, "level": 4, "name": "出版物零售店长"}, {"code": 6666600220, "positionType": 4, "level": 4, "name": "烟草专卖店店长"}, {"code": 6666600569, "positionType": 4, "level": 4, "name": "健身中心店长"}, {"code": 6666601043, "positionType": 4, "level": 4, "name": "绿植门店店长/主管"}], "positionType": 3, "level": 3, "name": "门店店长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290226, "positionType": 3, "level": 3, "name": "储备店长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290305, "subLevelModelList": [{"code": 6666600538, "positionType": 4, "level": 4, "name": "调味品批发市场督导"}], "positionType": 3, "level": 3, "name": "督导/巡店", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290309, "positionType": 3, "level": 3, "name": "卖场经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290314, "subLevelModelList": [{"code": 6666600850, "positionType": 4, "level": 4, "name": "体育场馆运营专员"}, {"code": 6666600956, "positionType": 4, "level": 4, "name": "运营专员"}], "positionType": 3, "level": 3, "name": "商场运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "零售", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001060, "subLevelModelList": [{"code": 210405, "positionType": 3, "level": 3, "name": "美容师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210410, "positionType": 3, "level": 3, "name": "美容店长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210408, "positionType": 3, "level": 3, "name": "美体师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210414, "positionType": 3, "level": 3, "name": "美容顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290802, "positionType": 3, "level": 3, "name": "美容导师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210608, "positionType": 3, "level": 3, "name": "美甲美睫师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210407, "positionType": 3, "level": 3, "name": "纹绣师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210607, "positionType": 3, "level": 3, "name": "发型师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210409, "positionType": 3, "level": 3, "name": "美发助理/学徒", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290801, "positionType": 3, "level": 3, "name": "养发师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210609, "positionType": 3, "level": 3, "name": "化妆/造型/服装", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "美容美发", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600592, "subLevelModelList": [{"code": 210403, "positionType": 3, "level": 3, "name": "理疗师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290118, "positionType": 3, "level": 3, "name": "产后康复师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210404, "positionType": 3, "level": 3, "name": "针灸推拿", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210412, "subLevelModelList": [{"code": 6666600711, "positionType": 4, "level": 4, "name": "保健按摩师"}], "positionType": 3, "level": 3, "name": "按摩师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210411, "positionType": 3, "level": 3, "name": "足疗师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210415, "positionType": 3, "level": 3, "name": "采耳师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210401, "subLevelModelList": [{"code": 6666600169, "positionType": 4, "level": 4, "name": "营养师（婴幼儿食品）"}, {"code": 6666600581, "positionType": 4, "level": 4, "name": "中西医结合健康顾问"}, {"code": 6666600845, "positionType": 4, "level": 4, "name": "营养标签专员"}], "positionType": 3, "level": 3, "name": "营养师/健康管理师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210305, "positionType": 3, "level": 3, "name": "康复治疗师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "理疗保健", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001100, "subLevelModelList": [{"code": 290106, "subLevelModelList": [{"code": 6666600724, "positionType": 4, "level": 4, "name": "保洁员"}], "positionType": 3, "level": 3, "name": "保洁", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290122, "subLevelModelList": [{"code": 6666600910, "positionType": 4, "level": 4, "name": "家政服务督导"}], "positionType": 3, "level": 3, "name": "保洁主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290108, "positionType": 3, "level": 3, "name": "保姆", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290109, "positionType": 3, "level": 3, "name": "月嫂", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290110, "positionType": 3, "level": 3, "name": "育婴师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290111, "positionType": 3, "level": 3, "name": "护工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290169, "positionType": 3, "level": 3, "name": "收纳师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "家政/保洁", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600572, "subLevelModelList": [{"code": 290105, "subLevelModelList": [{"code": 6666601061, "positionType": 4, "level": 4, "name": "保安员"}], "positionType": 3, "level": 3, "name": "保安", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290117, "subLevelModelList": [{"code": 6666600814, "positionType": 4, "level": 4, "name": "保卫主管"}], "positionType": 3, "level": 3, "name": "保安主管/队长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290121, "subLevelModelList": [{"code": 6666600215, "positionType": 4, "level": 4, "name": "智能岗亭值守员"}], "positionType": 3, "level": 3, "name": "消防中控员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290120, "positionType": 3, "level": 3, "name": "押运员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290112, "positionType": 3, "level": 3, "name": "安检员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290123, "positionType": 3, "level": 3, "name": "消防维保员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "安保服务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600972, "subLevelModelList": [{"code": 290114, "subLevelModelList": [{"code": 6666600213, "positionType": 4, "level": 4, "name": "家用电器维修工程师"}, {"code": 6666600817, "positionType": 4, "level": 4, "name": "施工机械维修调度员"}], "positionType": 3, "level": 3, "name": "家电维修", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290113, "positionType": 3, "level": 3, "name": "手机维修", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290166, "positionType": 3, "level": 3, "name": "电脑/打印机维修", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290124, "positionType": 3, "level": 3, "name": "电动车/摩托车维修", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "维修服务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001160, "subLevelModelList": [{"code": 230204, "subLevelModelList": [{"code": 6666600389, "positionType": 4, "level": 4, "name": "汽车维修技师"}, {"code": 6666600717, "positionType": 4, "level": 4, "name": "车辆维修技师"}, {"code": 6666600918, "positionType": 4, "level": 4, "name": "维修质检专员"}], "positionType": 3, "level": 3, "name": "汽车维修", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230205, "positionType": 3, "level": 3, "name": "汽车美容", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230213, "positionType": 3, "level": 3, "name": "洗车工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230209, "positionType": 3, "level": 3, "name": "汽车改装", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230203, "subLevelModelList": [{"code": 6666600475, "positionType": 4, "level": 4, "name": "汽车租赁专员"}, {"code": 6666600755, "positionType": 4, "level": 4, "name": "汽车技术咨询顾问"}, {"code": 6666600815, "positionType": 4, "level": 4, "name": "大客户经理（汽车后市场）"}], "positionType": 3, "level": 3, "name": "汽车服务顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230208, "positionType": 3, "level": 3, "name": "4S店店长/维修站长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230207, "subLevelModelList": [{"code": 6666600218, "positionType": 4, "level": 4, "name": "二手车评估师"}, {"code": 6666600923, "positionType": 4, "level": 4, "name": "二手设备评估师"}], "positionType": 3, "level": 3, "name": "二手车评估师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230206, "positionType": 3, "level": 3, "name": "汽车查勘定损", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 230214, "positionType": 3, "level": 3, "name": "加油员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "汽车服务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001090, "subLevelModelList": [{"code": 290601, "positionType": 3, "level": 3, "name": "宠物美容", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290602, "positionType": 3, "level": 3, "name": "宠物医生", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "宠物服务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001070, "subLevelModelList": [{"code": 190705, "positionType": 3, "level": 3, "name": "健身教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190701, "positionType": 3, "level": 3, "name": "舞蹈老师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190706, "positionType": 3, "level": 3, "name": "篮球教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210601, "positionType": 3, "level": 3, "name": "瑜伽老师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210603, "positionType": 3, "level": 3, "name": "游泳教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190707, "positionType": 3, "level": 3, "name": "跆拳道教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190708, "positionType": 3, "level": 3, "name": "武术教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190709, "positionType": 3, "level": 3, "name": "轮滑教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210613, "positionType": 3, "level": 3, "name": "救生员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190719, "positionType": 3, "level": 3, "name": "乒乓球教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190766, "positionType": 3, "level": 3, "name": "足球教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190720, "positionType": 3, "level": 3, "name": "羽毛球教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190769, "positionType": 3, "level": 3, "name": "拳击教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 190312, "subLevelModelList": [{"code": 6666600971, "positionType": 4, "level": 4, "name": "私人体能训练教练"}], "positionType": 3, "level": 3, "name": "体育/体能老师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "运动健身", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600752, "subLevelModelList": [{"code": 240305, "positionType": 3, "level": 3, "name": "网约车司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240306, "positionType": 3, "level": 3, "name": "代驾司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240307, "positionType": 3, "level": 3, "name": "驾校教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150208, "positionType": 3, "level": 3, "name": "商务司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240301, "subLevelModelList": [{"code": 6666600401, "positionType": 4, "level": 4, "name": "货运业务员"}, {"code": 6666600951, "positionType": 4, "level": 4, "name": "货运车辆自动驾驶测试员"}, {"code": 6666600998, "positionType": 4, "level": 4, "name": "货运客服专员"}, {"code": 6666601020, "positionType": 4, "level": 4, "name": "货运安检员"}, {"code": 6666601092, "positionType": 4, "level": 4, "name": "货车司机"}], "positionType": 3, "level": 3, "name": "货运司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240308, "positionType": 3, "level": 3, "name": "客运司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100311, "subLevelModelList": [{"code": 6666600055, "positionType": 4, "level": 4, "name": "机手培训教官"}, {"code": 6666600126, "positionType": 4, "level": 4, "name": "无人机测绘员"}], "positionType": 3, "level": 3, "name": "无人机飞手", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "驾驶员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001110, "subLevelModelList": [{"code": 290701, "positionType": 3, "level": 3, "name": "花艺师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290702, "positionType": 3, "level": 3, "name": "婚礼策划", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290313, "positionType": 3, "level": 3, "name": "网吧网管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210610, "positionType": 3, "level": 3, "name": "会籍顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280103, "positionType": 3, "level": 3, "name": "旅游顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 210109, "positionType": 3, "level": 3, "name": "验光师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170602, "positionType": 3, "level": 3, "name": "摄影/摄像师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170626, "positionType": 3, "level": 3, "name": "剧本杀主持人", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170628, "subLevelModelList": [{"code": 6666600741, "positionType": 4, "level": 4, "name": "儿童安全督导员"}], "positionType": 3, "level": 3, "name": "儿童引导师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170613, "positionType": 3, "level": 3, "name": "放映员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290402, "positionType": 3, "level": 3, "name": "游戏陪玩", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290401, "subLevelModelList": [{"code": 6666600663, "positionType": 4, "level": 4, "name": "售后服务调度"}, {"code": 6666600688, "positionType": 4, "level": 4, "name": "售后服务协调员"}, {"code": 6666600769, "positionType": 4, "level": 4, "name": "展览展示服务专员"}, {"code": 6666600779, "positionType": 4, "level": 4, "name": "驻场服务工程师"}, {"code": 6666600938, "positionType": 4, "level": 4, "name": "租赁设备 GPS 监控专员"}, {"code": 6666601094, "positionType": 4, "level": 4, "name": "固体废物处理工程师"}], "positionType": 3, "level": 3, "name": "其他服务业职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他服务业职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "零售/生活服务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2142200, "subLevelModelList": [{"code": 2600512, "subLevelModelList": [{"code": 290202, "subLevelModelList": [{"code": 6666600381, "positionType": 4, "level": 4, "name": "餐饮服务员"}, {"code": 6666600936, "positionType": 4, "level": 4, "name": "餐饮服务专员"}], "positionType": 3, "level": 3, "name": "服务员", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290201, "positionType": 3, "level": 3, "name": "收银", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290107, "subLevelModelList": [{"code": 6666600518, "positionType": 4, "level": 4, "name": "礼仪服务主管"}], "positionType": 3, "level": 3, "name": "礼仪/迎宾/接待", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290216, "positionType": 3, "level": 3, "name": "传菜员", "searchKey": "canyin", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "前厅", "searchKey": "canyin"}, {"code": 2600522, "subLevelModelList": [{"code": 290212, "positionType": 3, "level": 3, "name": "餐饮学徒", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290203, "positionType": 3, "level": 3, "name": "厨师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290219, "positionType": 3, "level": 3, "name": "中餐厨师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290222, "positionType": 3, "level": 3, "name": "烧烤师傅", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290220, "positionType": 3, "level": 3, "name": "西餐厨师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290221, "positionType": 3, "level": 3, "name": "日料厨师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290218, "positionType": 3, "level": 3, "name": "凉菜厨师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290213, "positionType": 3, "level": 3, "name": "面点师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290208, "positionType": 3, "level": 3, "name": "后厨", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290209, "positionType": 3, "level": 3, "name": "配菜打荷", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290217, "positionType": 3, "level": 3, "name": "洗碗工", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290224, "subLevelModelList": [{"code": 6666600622, "positionType": 4, "level": 4, "name": "特种水产养殖技术员"}], "positionType": 3, "level": 3, "name": "水台/水产员", "searchKey": "canyin", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "后厨", "searchKey": "canyin"}, {"code": 2600532, "subLevelModelList": [{"code": 290206, "subLevelModelList": [{"code": 6666600074, "positionType": 4, "level": 4, "name": "餐饮管理"}], "positionType": 3, "level": 3, "name": "餐饮店长", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290207, "subLevelModelList": [{"code": 6666600841, "positionType": 4, "level": 4, "name": "餐饮经理"}], "positionType": 3, "level": 3, "name": "餐饮前厅经理/领班", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290226, "positionType": 3, "level": 3, "name": "储备店长", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290215, "positionType": 3, "level": 3, "name": "厨师长", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290214, "positionType": 3, "level": 3, "name": "行政总厨", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290228, "positionType": 3, "level": 3, "name": "餐饮督导", "searchKey": "canyin", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "餐饮管理", "searchKey": "canyin"}, {"code": 2600552, "subLevelModelList": [{"code": 290204, "positionType": 3, "level": 3, "name": "咖啡师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290210, "positionType": 3, "level": 3, "name": "茶艺师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290223, "positionType": 3, "level": 3, "name": "奶茶店店员", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290227, "positionType": 3, "level": 3, "name": "调酒师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290225, "subLevelModelList": [{"code": 6666600904, "positionType": 4, "level": 4, "name": " 烘焙工程师"}], "positionType": 3, "level": 3, "name": "面包/烘焙师", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 290211, "positionType": 3, "level": 3, "name": "蛋糕/裱花师", "searchKey": "canyin", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "甜点饮品", "searchKey": "canyin"}, {"code": 2600542, "subLevelModelList": [{"code": 290205, "positionType": 3, "level": 3, "name": "送餐员", "searchKey": "canyin", "recruitmentType": "1,2,3"}, {"code": 130134, "positionType": 3, "level": 3, "name": "外卖运营", "searchKey": "canyin", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他餐饮岗位", "searchKey": "canyin"}], "positionType": 0, "level": 1, "name": "餐饮", "searchKey": "canyin"}, {"code": 1170000, "subLevelModelList": [{"code": 1001040, "subLevelModelList": [{"code": 290102, "positionType": 3, "level": 3, "name": "酒店前台", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290107, "subLevelModelList": [{"code": 6666600518, "positionType": 4, "level": 4, "name": "礼仪服务主管"}], "positionType": 3, "level": 3, "name": "礼仪/迎宾/接待", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290103, "subLevelModelList": [{"code": 6666600345, "positionType": 4, "level": 4, "name": "酒店服务专员"}], "positionType": 3, "level": 3, "name": "客房服务员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290104, "subLevelModelList": [{"code": 6666600520, "positionType": 4, "level": 4, "name": "酒店运营经理"}], "positionType": 3, "level": 3, "name": "酒店经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290115, "positionType": 3, "level": 3, "name": "酒店前厅经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290116, "positionType": 3, "level": 3, "name": "客房经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290158, "positionType": 3, "level": 3, "name": "民宿管家", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "酒店", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001000, "subLevelModelList": [{"code": 280103, "positionType": 3, "level": 3, "name": "旅游顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280104, "positionType": 3, "level": 3, "name": "导游", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280201, "positionType": 3, "level": 3, "name": "旅游产品经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280106, "positionType": 3, "level": 3, "name": "讲解员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280101, "positionType": 3, "level": 3, "name": "计调", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280105, "positionType": 3, "level": 3, "name": "票务员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 280102, "positionType": 3, "level": 3, "name": "签证专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "旅游服务", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001020, "subLevelModelList": [{"code": 280301, "positionType": 3, "level": 3, "name": "其他旅游职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他旅游职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "酒店/旅游", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1100000, "subLevelModelList": [{"code": 1000680, "subLevelModelList": [{"code": 190301, "subLevelModelList": [{"code": 6666600221, "positionType": 4, "level": 4, "name": "学科辅导老师"}], "positionType": 3, "level": 3, "name": "教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190309, "positionType": 3, "level": 3, "name": "英语教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190302, "positionType": 3, "level": 3, "name": "助教", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190317, "positionType": 3, "level": 3, "name": "数学教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190316, "positionType": 3, "level": 3, "name": "语文教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190303, "positionType": 3, "level": 3, "name": "高中教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190304, "positionType": 3, "level": 3, "name": "初中教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190305, "positionType": 3, "level": 3, "name": "小学教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190321, "positionType": 3, "level": 3, "name": "家教", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190318, "positionType": 3, "level": 3, "name": "物理教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190319, "positionType": 3, "level": 3, "name": "化学教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190307, "positionType": 3, "level": 3, "name": "理科教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190314, "positionType": 3, "level": 3, "name": "日语教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190320, "positionType": 3, "level": 3, "name": "生物教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190308, "positionType": 3, "level": 3, "name": "文科教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190245, "positionType": 3, "level": 3, "name": "地理教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190315, "positionType": 3, "level": 3, "name": "其他外语教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "教师", "searchKey": "jiaoyupeixun"}, {"code": 2600892, "subLevelModelList": [{"code": 190306, "positionType": 3, "level": 3, "name": "幼教", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190322, "positionType": 3, "level": 3, "name": "托管老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190323, "positionType": 3, "level": 3, "name": "早教老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190326, "positionType": 3, "level": 3, "name": "保育员", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190324, "positionType": 3, "level": 3, "name": "感统训练教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "幼少儿教师", "searchKey": "jiaoyupeixun"}, {"code": 1000670, "subLevelModelList": [{"code": 190202, "positionType": 3, "level": 3, "name": "教务管理", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190201, "subLevelModelList": [{"code": 6666600933, "positionType": 4, "level": 4, "name": "托管中心校长"}], "positionType": 3, "level": 3, "name": "校长/副校长", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190204, "subLevelModelList": [{"code": 6666600571, "positionType": 4, "level": 4, "name": "托管班主任"}, {"code": 6666600833, "positionType": 4, "level": 4, "name": "课业辅导师"}, {"code": 6666600992, "positionType": 4, "level": 4, "name": "课后辅导总监"}], "positionType": 3, "level": 3, "name": "班主任/辅导员", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190203, "subLevelModelList": [{"code": 6666600594, "positionType": 4, "level": 4, "name": "教学评价专员"}, {"code": 6666601085, "positionType": 4, "level": 4, "name": "教育供应链优化专员"}], "positionType": 3, "level": 3, "name": "教学管理", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190205, "positionType": 3, "level": 3, "name": "园长/副园长", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190313, "positionType": 3, "level": 3, "name": "就业老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "教育行政", "searchKey": "jiaoyupeixun"}, {"code": 2600812, "subLevelModelList": [{"code": 190705, "positionType": 3, "level": 3, "name": "健身教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190706, "positionType": 3, "level": 3, "name": "篮球教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190707, "positionType": 3, "level": 3, "name": "跆拳道教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190708, "positionType": 3, "level": 3, "name": "武术教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190709, "positionType": 3, "level": 3, "name": "轮滑教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190719, "positionType": 3, "level": 3, "name": "乒乓球教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190766, "positionType": 3, "level": 3, "name": "足球教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190720, "positionType": 3, "level": 3, "name": "羽毛球教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190769, "positionType": 3, "level": 3, "name": "拳击教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190770, "positionType": 3, "level": 3, "name": "台球教练/助教", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 210603, "positionType": 3, "level": 3, "name": "游泳教练", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 210601, "positionType": 3, "level": 3, "name": "瑜伽老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 210613, "positionType": 3, "level": 3, "name": "救生员", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 210612, "positionType": 3, "level": 3, "name": "普拉提老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190312, "subLevelModelList": [{"code": 6666600971, "positionType": 4, "level": 4, "name": "私人体能训练教练"}], "positionType": 3, "level": 3, "name": "体育/体能老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "运动健身", "searchKey": "jiaoyupeixun"}, {"code": 2600822, "subLevelModelList": [{"code": 190311, "positionType": 3, "level": 3, "name": "美术教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190701, "positionType": 3, "level": 3, "name": "舞蹈老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190712, "positionType": 3, "level": 3, "name": "书法教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190310, "positionType": 3, "level": 3, "name": "音乐教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190716, "positionType": 3, "level": 3, "name": "播音主持教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190713, "positionType": 3, "level": 3, "name": "钢琴教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190715, "positionType": 3, "level": 3, "name": "古筝教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190714, "positionType": 3, "level": 3, "name": "吉他教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190710, "positionType": 3, "level": 3, "name": "表演教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190767, "positionType": 3, "level": 3, "name": "架子鼓老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190768, "positionType": 3, "level": 3, "name": "围棋老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "文化艺术", "searchKey": "jiaoyupeixun"}, {"code": 2600832, "subLevelModelList": [{"code": 190717, "positionType": 3, "level": 3, "name": "乐高教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190711, "positionType": 3, "level": 3, "name": "机器人教师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190718, "positionType": 3, "level": 3, "name": "少儿编程老师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "科学探索培训", "searchKey": "jiaoyupeixun"}, {"code": 1000700, "subLevelModelList": [{"code": 190105, "subLevelModelList": [{"code": 6666600127, "positionType": 4, "level": 4, "name": "建筑机械安装技术培训讲师"}, {"code": 6666600295, "positionType": 4, "level": 4, "name": "培训讲师（驾驶员培训）"}, {"code": 6666600576, "positionType": 4, "level": 4, "name": "美术用品销售/培训师"}, {"code": 6666600612, "positionType": 4, "level": 4, "name": "培训讲师"}, {"code": 6666600698, "positionType": 4, "level": 4, "name": "销售培训师"}, {"code": 6666600960, "positionType": 4, "level": 4, "name": "技术培训讲师"}], "positionType": 3, "level": 3, "name": "培训师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190504, "positionType": 3, "level": 3, "name": "拓展培训", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190406, "positionType": 3, "level": 3, "name": "IT培训讲师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190501, "positionType": 3, "level": 3, "name": "财会培训讲师", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "职业培训", "searchKey": "jiaoyupeixun"}, {"code": 1000660, "subLevelModelList": [{"code": 190101, "subLevelModelList": [{"code": 6666600060, "positionType": 4, "level": 4, "name": "课程研发专员"}, {"code": 6666600342, "positionType": 4, "level": 4, "name": "课程研发助理"}, {"code": 6666600461, "positionType": 4, "level": 4, "name": "教育混合式教学设计师"}], "positionType": 3, "level": 3, "name": "课程设计", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190102, "positionType": 3, "level": 3, "name": "课程编辑", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190107, "positionType": 3, "level": 3, "name": "培训策划", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190104, "subLevelModelList": [{"code": 6666600236, "positionType": 4, "level": 4, "name": "教育政策研究员"}, {"code": 6666600404, "positionType": 4, "level": 4, "name": "教育游戏化设计师"}, {"code": 6666600537, "positionType": 4, "level": 4, "name": "教育科研研究员"}], "positionType": 3, "level": 3, "name": "培训研究", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190106, "subLevelModelList": [{"code": 6666600117, "positionType": 4, "level": 4, "name": "教育元宇宙课程架构师"}, {"code": 6666600154, "positionType": 4, "level": 4, "name": "教育无障碍设计工程师"}, {"code": 6666600658, "positionType": 4, "level": 4, "name": "教育 Z 世代运营专家"}, {"code": 6666600678, "positionType": 4, "level": 4, "name": "教育产品测试工程师"}, {"code": 6666600983, "positionType": 4, "level": 4, "name": "教育硬件产品经理"}, {"code": 6666601028, "positionType": 4, "level": 4, "name": "国际教育课程协调员"}], "positionType": 3, "level": 3, "name": "其他教育产品研发职位", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "教育产品研发", "searchKey": "jiaoyupeixun"}, {"code": 2600452, "subLevelModelList": [{"code": 190601, "subLevelModelList": [{"code": 6666600238, "positionType": 4, "level": 4, "name": "课程咨询师"}, {"code": 6666600288, "positionType": 4, "level": 4, "name": "课程顾问 "}, {"code": 6666600436, "positionType": 4, "level": 4, "name": "销售产品培训顾问"}, {"code": 6666600491, "positionType": 4, "level": 4, "name": "教育咨询顾问"}, {"code": 6666600937, "positionType": 4, "level": 4, "name": "课程销售顾问"}, {"code": 6666600958, "positionType": 4, "level": 4, "name": "教育老龄教育课程顾问"}], "positionType": 3, "level": 3, "name": "课程顾问", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}, {"code": 190603, "subLevelModelList": [{"code": 6666600484, "positionType": 4, "level": 4, "name": "留学规划师"}], "positionType": 3, "level": 3, "name": "留学顾问", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "教培销售", "searchKey": "jiaoyupeixun"}, {"code": 1000720, "subLevelModelList": [{"code": 190801, "subLevelModelList": [{"code": 6666600153, "positionType": 4, "level": 4, "name": "销售培训助理"}, {"code": 6666600611, "positionType": 4, "level": 4, "name": "培训专员"}, {"code": 6666600952, "positionType": 4, "level": 4, "name": "教育数据安全专员"}, {"code": 6666600962, "positionType": 4, "level": 4, "name": "教育数据分析员"}, {"code": 6666601063, "positionType": 4, "level": 4, "name": "教育 XR 技术应用专员"}], "positionType": 3, "level": 3, "name": "其他教育培训职位", "searchKey": "jiaoyupeixun", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他教育培训职位", "searchKey": "jiaoyupeixun"}], "positionType": 0, "level": 1, "name": "教育培训", "searchKey": "jiaoyupeixun"}, {"code": 1030000, "subLevelModelList": [{"code": 1000190, "subLevelModelList": [{"code": 120106, "subLevelModelList": [{"code": 6666600672, "positionType": 4, "level": 4, "name": "平面设计师（房地产方向）"}, {"code": 6666600683, "positionType": 4, "level": 4, "name": "平面设计师"}, {"code": 6666600877, "positionType": 4, "level": 4, "name": "活动平面设计师"}, {"code": 6666600981, "positionType": 4, "level": 4, "name": "品牌视觉设计主管"}], "positionType": 3, "level": 3, "name": "平面设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120117, "subLevelModelList": [{"code": 6666600025, "positionType": 4, "level": 4, "name": "电商美工"}, {"code": 6666600173, "positionType": 4, "level": 4, "name": "电商美工（农产品视觉设计）"}], "positionType": 3, "level": 3, "name": "美工", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120119, "subLevelModelList": [{"code": 6666600689, "positionType": 4, "level": 4, "name": "现场设计师"}, {"code": 6666600695, "positionType": 4, "level": 4, "name": "教育装备方案设计师"}, {"code": 6666600729, "positionType": 4, "level": 4, "name": "液压系统设计师"}], "positionType": 3, "level": 3, "name": "设计师助理", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120105, "subLevelModelList": [{"code": 6666600001, "positionType": 4, "level": 4, "name": "UI设计师"}], "positionType": 3, "level": 3, "name": "UI设计师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120101, "subLevelModelList": [{"code": 6666600016, "positionType": 4, "level": 4, "name": "企业形象设计师"}], "positionType": 3, "level": 3, "name": "视觉设计师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 140603, "subLevelModelList": [{"code": 6666600278, "positionType": 4, "level": 4, "name": "图文设计师"}, {"code": 6666600386, "positionType": 4, "level": 4, "name": "广告设计"}, {"code": 6666600732, "positionType": 4, "level": 4, "name": "广告设计师"}, {"code": 6666600839, "positionType": 4, "level": 4, "name": "广告设计专员"}], "positionType": 3, "level": 3, "name": "广告设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120201, "positionType": 3, "level": 3, "name": "UX/交互设计师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120302, "positionType": 3, "level": 3, "name": "用户研究", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "视觉/交互设计", "searchKey": "sheji"}, {"code": 2600652, "subLevelModelList": [{"code": 220205, "subLevelModelList": [{"code": 6666600328, "positionType": 4, "level": 4, "name": "室内设计师"}], "positionType": 3, "level": 3, "name": "室内设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 220217, "subLevelModelList": [{"code": 6666600327, "positionType": 4, "level": 4, "name": "软装设计师"}], "positionType": 3, "level": 3, "name": "软装设计师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120611, "positionType": 3, "level": 3, "name": "展览/展示设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120608, "positionType": 3, "level": 3, "name": "陈列设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120612, "positionType": 3, "level": 3, "name": "照明设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 220206, "subLevelModelList": [{"code": 6666600212, "positionType": 4, "level": 4, "name": "园林绿化养护技术员"}, {"code": 6666600297, "positionType": 4, "level": 4, "name": "园林工程师"}, {"code": 6666600427, "positionType": 4, "level": 4, "name": "园艺师"}, {"code": 6666600623, "positionType": 4, "level": 4, "name": "景观设计师"}, {"code": 6666600880, "positionType": 4, "level": 4, "name": "绿化工程师"}, {"code": 6666601008, "positionType": 4, "level": 4, "name": "园林绿化项目经理"}], "positionType": 3, "level": 3, "name": "园林/景观设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120614, "subLevelModelList": [{"code": 6666600312, "positionType": 4, "level": 4, "name": "舞台设计师"}], "positionType": 3, "level": 3, "name": "舞美设计师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120116, "positionType": 3, "level": 3, "name": "CAD绘图员", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "环境设计", "searchKey": "sheji"}, {"code": 2600962, "subLevelModelList": [{"code": 120604, "subLevelModelList": [{"code": 6666600667, "positionType": 4, "level": 4, "name": "定制家具设计师"}], "positionType": 3, "level": 3, "name": "家具设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120602, "subLevelModelList": [{"code": 6666600780, "positionType": 4, "level": 4, "name": "工业设计工程师（外观）"}], "positionType": 3, "level": 3, "name": "工业设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120118, "subLevelModelList": [{"code": 6666600495, "positionType": 4, "level": 4, "name": "食品营养标签设计师"}, {"code": 6666600511, "positionType": 4, "level": 4, "name": "食品包装设计师"}, {"code": 6666600799, "positionType": 4, "level": 4, "name": "包装设计主管"}], "positionType": 3, "level": 3, "name": "包装设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120606, "positionType": 3, "level": 3, "name": "珠宝设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120613, "positionType": 3, "level": 3, "name": "家具拆单员", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "工业设计", "searchKey": "sheji"}, {"code": 2600642, "subLevelModelList": [{"code": 300501, "positionType": 3, "level": 3, "name": "服装/纺织设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120615, "positionType": 3, "level": 3, "name": "鞋类设计师", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "服装设计", "searchKey": "sheji"}, {"code": 2600622, "subLevelModelList": [{"code": 120107, "subLevelModelList": [{"code": 6666600358, "positionType": 4, "level": 4, "name": "3D打印技术工程师"}, {"code": 6666600419, "positionType": 4, "level": 4, "name": "3D打印技术支持工程师"}, {"code": 6666600514, "positionType": 4, "level": 4, "name": "3D打印工艺工程师"}, {"code": 6666600555, "positionType": 4, "level": 4, "name": "3D打印技术员"}, {"code": 6666600736, "positionType": 4, "level": 4, "name": "模具设计师"}, {"code": 6666600909, "positionType": 4, "level": 4, "name": "3D打印材料管理员"}, {"code": 6666601048, "positionType": 4, "level": 4, "name": "工程动画制作师"}], "positionType": 3, "level": 3, "name": "3D设计师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120121, "positionType": 3, "level": 3, "name": "插画师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120120, "positionType": 3, "level": 3, "name": "动画设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120110, "positionType": 3, "level": 3, "name": "原画师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120122, "positionType": 3, "level": 3, "name": "漫画师", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120123, "positionType": 3, "level": 3, "name": "修图师", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "美术/3D/动画", "searchKey": "sheji"}, {"code": 1000200, "subLevelModelList": [{"code": 120113, "positionType": 3, "level": 3, "name": "游戏场景", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120114, "positionType": 3, "level": 3, "name": "游戏角色", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120112, "positionType": 3, "level": 3, "name": "游戏UI设计", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120111, "positionType": 3, "level": 3, "name": "游戏特效", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120115, "positionType": 3, "level": 3, "name": "游戏动作", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120306, "positionType": 3, "level": 3, "name": "游戏主美术", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "游戏设计", "searchKey": "sheji"}, {"code": 1000220, "subLevelModelList": [{"code": 120401, "positionType": 3, "level": 3, "name": "设计经理/主管", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120402, "positionType": 3, "level": 3, "name": "设计总监", "searchKey": "sheji", "recruitmentType": "1,2,3"}, {"code": 120404, "positionType": 3, "level": 3, "name": "视觉设计总监", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "高端设计职位", "searchKey": "sheji"}, {"code": 1000240, "subLevelModelList": [{"code": 120501, "subLevelModelList": [{"code": 6666600906, "positionType": 4, "level": 4, "name": "工程机械液压系统设计师"}], "positionType": 3, "level": 3, "name": "其他设计职位", "searchKey": "sheji", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他设计职位", "searchKey": "sheji"}], "positionType": 0, "level": 1, "name": "设计", "searchKey": "sheji"}, {"code": 1140000, "subLevelModelList": [{"code": 2600672, "subLevelModelList": [{"code": 220212, "subLevelModelList": [{"code": 6666600062, "positionType": 4, "level": 4, "name": "桥梁工程技术负责人"}, {"code": 6666600096, "positionType": 4, "level": 4, "name": "铁路工程项目经理"}, {"code": 6666600201, "positionType": 4, "level": 4, "name": "工程项目经理"}, {"code": 6666600210, "positionType": 4, "level": 4, "name": "工程项目经理/工程主管"}, {"code": 6666600267, "positionType": 4, "level": 4, "name": "工程项目经理（储运设施建设）"}, {"code": 6666600490, "positionType": 4, "level": 4, "name": "建筑工程项目经理"}, {"code": 6666600492, "positionType": 4, "level": 4, "name": "房地产开发项目经理"}, {"code": 6666600536, "positionType": 4, "level": 4, "name": "项目副经理"}, {"code": 6666600613, "positionType": 4, "level": 4, "name": "项目经理"}, {"code": 6666600810, "positionType": 4, "level": 4, "name": "装配式建筑构件销售经理"}, {"code": 6666600821, "positionType": 4, "level": 4, "name": "建筑机具租赁业务经理"}, {"code": 6666600902, "positionType": 4, "level": 4, "name": "工程管理专员"}], "positionType": 3, "level": 3, "name": "建筑施工项目经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220209, "subLevelModelList": [{"code": 6666600214, "positionType": 4, "level": 4, "name": "预算员"}, {"code": 6666600489, "positionType": 4, "level": 4, "name": "造价工程师"}, {"code": 6666600631, "positionType": 4, "level": 4, "name": "土建造价工程师"}, {"code": 6666600997, "positionType": 4, "level": 4, "name": "安装造价工程师"}], "positionType": 3, "level": 3, "name": "工程造价", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220208, "subLevelModelList": [{"code": 6666600208, "positionType": 4, "level": 4, "name": "总监理工程师"}, {"code": 6666600293, "positionType": 4, "level": 4, "name": "绿化工程监理"}, {"code": 6666600303, "positionType": 4, "level": 4, "name": "工程监理"}, {"code": 6666600375, "positionType": 4, "level": 4, "name": "施工监理"}, {"code": 6666600707, "positionType": 4, "level": 4, "name": "建设工程监理工程师"}], "positionType": 3, "level": 3, "name": "工程监理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 260114, "subLevelModelList": [{"code": 6666600209, "positionType": 4, "level": 4, "name": "工程咨询师"}, {"code": 6666600343, "positionType": 4, "level": 4, "name": "咨询顾问"}], "positionType": 3, "level": 3, "name": "工程咨询", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220218, "subLevelModelList": [{"code": 6666600029, "positionType": 4, "level": 4, "name": "栋号长"}, {"code": 6666600033, "positionType": 4, "level": 4, "name": "安装施工员"}, {"code": 6666600172, "positionType": 4, "level": 4, "name": "房屋建筑工程施工员"}, {"code": 6666600365, "positionType": 4, "level": 4, "name": "施工调度员"}, {"code": 6666600432, "positionType": 4, "level": 4, "name": "混凝土工长"}, {"code": 6666600485, "positionType": 4, "level": 4, "name": "土建施工员"}, {"code": 6666600527, "positionType": 4, "level": 4, "name": "砌筑工长"}, {"code": 6666600565, "positionType": 4, "level": 4, "name": "施工员"}, {"code": 6666600636, "positionType": 4, "level": 4, "name": "园林绿化施工员 / 绿化工程项目经理"}, {"code": 6666600654, "positionType": 4, "level": 4, "name": "市政施工员"}, {"code": 6666600665, "positionType": 4, "level": 4, "name": "装饰装修施工员"}, {"code": 6666600716, "positionType": 4, "level": 4, "name": "施工技术员"}, {"code": 6666600746, "positionType": 4, "level": 4, "name": "土石方工程施工员"}, {"code": 6666600944, "positionType": 4, "level": 4, "name": "绿化施工员"}, {"code": 6666600969, "positionType": 4, "level": 4, "name": "施工机械售后服务工程师"}], "positionType": 3, "level": 3, "name": "施工员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220211, "subLevelModelList": [{"code": 6666600340, "positionType": 4, "level": 4, "name": "工程资料整理员"}, {"code": 6666600588, "positionType": 4, "level": 4, "name": "资料员"}], "positionType": 3, "level": 3, "name": "资料员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220220, "subLevelModelList": [{"code": 6666600655, "positionType": 4, "level": 4, "name": "材料员"}], "positionType": 3, "level": 3, "name": "材料员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220219, "subLevelModelList": [{"code": 6666600325, "positionType": 4, "level": 4, "name": "测量工程师"}, {"code": 6666600457, "positionType": 4, "level": 4, "name": "测量员"}], "positionType": 3, "level": 3, "name": "测绘/测量", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220225, "subLevelModelList": [{"code": 6666600329, "positionType": 4, "level": 4, "name": "施工机械安装安全员"}, {"code": 6666600353, "positionType": 4, "level": 4, "name": "安全巡检员（机械设备）"}], "positionType": 3, "level": 3, "name": "施工安全员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220226, "subLevelModelList": [{"code": 6666600219, "positionType": 4, "level": 4, "name": "工程质量巡检员"}], "positionType": 3, "level": 3, "name": "工程检测员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "工程管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000900, "subLevelModelList": [{"code": 220205, "subLevelModelList": [{"code": 6666600328, "positionType": 4, "level": 4, "name": "室内设计师"}], "positionType": 3, "level": 3, "name": "室内设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220217, "subLevelModelList": [{"code": 6666600327, "positionType": 4, "level": 4, "name": "软装设计师"}], "positionType": 3, "level": 3, "name": "软装设计师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220222, "subLevelModelList": [{"code": 6666600041, "positionType": 4, "level": 4, "name": "建筑装修装饰工程施工员"}, {"code": 6666601005, "positionType": 4, "level": 4, "name": "装修预算员"}], "positionType": 3, "level": 3, "name": "装修项目经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220702, "subLevelModelList": [{"code": 6666600510, "positionType": 4, "level": 4, "name": "装饰装修质量员"}, {"code": 6666600887, "positionType": 4, "level": 4, "name": "现场监理员"}], "positionType": 3, "level": 3, "name": "装修监理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220701, "positionType": 3, "level": 3, "name": "装修造价", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "装饰装修", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000910, "subLevelModelList": [{"code": 220401, "positionType": 3, "level": 3, "name": "物业经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220406, "subLevelModelList": [{"code": 6666600084, "positionType": 4, "level": 4, "name": "租房管家"}, {"code": 6666600299, "positionType": 4, "level": 4, "name": "物业管理专员"}, {"code": 6666600773, "positionType": 4, "level": 4, "name": "绿色施工管理员"}, {"code": 6666600893, "positionType": 4, "level": 4, "name": "物业管理主管"}], "positionType": 3, "level": 3, "name": "物业管理员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220404, "subLevelModelList": [{"code": 6666600159, "positionType": 4, "level": 4, "name": "筑路工"}], "positionType": 3, "level": 3, "name": "综合维修工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220407, "subLevelModelList": [{"code": 6666600948, "positionType": 4, "level": 4, "name": "维修呼叫中心主管"}], "positionType": 3, "level": 3, "name": "物业工程主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220403, "subLevelModelList": [{"code": 6666600465, "positionType": 4, "level": 4, "name": "房地产营销咨询经理"}], "positionType": 3, "level": 3, "name": "地产招商", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300635, "positionType": 3, "level": 3, "name": "弱电工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220405, "subLevelModelList": [{"code": 6666600129, "positionType": 4, "level": 4, "name": "园林绿化施工员"}, {"code": 6666600191, "positionType": 4, "level": 4, "name": "植物养护服务工程师"}, {"code": 6666600545, "positionType": 4, "level": 4, "name": "园林绿化养护员"}], "positionType": 3, "level": 3, "name": "绿化工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290105, "subLevelModelList": [{"code": 6666601061, "positionType": 4, "level": 4, "name": "保安员"}], "positionType": 3, "level": 3, "name": "保安", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290117, "subLevelModelList": [{"code": 6666600814, "positionType": 4, "level": 4, "name": "保卫主管"}], "positionType": 3, "level": 3, "name": "保安主管/队长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290121, "subLevelModelList": [{"code": 6666600215, "positionType": 4, "level": 4, "name": "智能岗亭值守员"}], "positionType": 3, "level": 3, "name": "消防中控员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290123, "positionType": 3, "level": 3, "name": "消防维保员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "物业管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600692, "subLevelModelList": [{"code": 220204, "subLevelModelList": [{"code": 6666600103, "positionType": 4, "level": 4, "name": "钢结构工程师"}, {"code": 6666600579, "positionType": 4, "level": 4, "name": "土建工程师"}, {"code": 6666600593, "positionType": 4, "level": 4, "name": "公路工程师"}, {"code": 6666600843, "positionType": 4, "level": 4, "name": "模具 CAE 分析工程师"}, {"code": 6666600970, "positionType": 4, "level": 4, "name": "结构工程师"}, {"code": 6666600996, "positionType": 4, "level": 4, "name": "道路工程师"}], "positionType": 3, "level": 3, "name": "土木/土建/结构工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220206, "subLevelModelList": [{"code": 6666600212, "positionType": 4, "level": 4, "name": "园林绿化养护技术员"}, {"code": 6666600297, "positionType": 4, "level": 4, "name": "园林工程师"}, {"code": 6666600427, "positionType": 4, "level": 4, "name": "园艺师"}, {"code": 6666600623, "positionType": 4, "level": 4, "name": "景观设计师"}, {"code": 6666600880, "positionType": 4, "level": 4, "name": "绿化工程师"}, {"code": 6666601008, "positionType": 4, "level": 4, "name": "园林绿化项目经理"}], "positionType": 3, "level": 3, "name": "园林/景观设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220203, "subLevelModelList": [{"code": 6666600197, "positionType": 4, "level": 4, "name": "建筑设计工程师"}, {"code": 6666600307, "positionType": 4, "level": 4, "name": "施工图深化设计师"}, {"code": 6666600939, "positionType": 4, "level": 4, "name": "建筑/空间设计总监"}, {"code": 6666601017, "positionType": 4, "level": 4, "name": "建筑设计师"}], "positionType": 3, "level": 3, "name": "建筑设计师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220202, "subLevelModelList": [{"code": 6666600216, "positionType": 4, "level": 4, "name": "绿色建筑工程师"}, {"code": 6666600367, "positionType": 4, "level": 4, "name": "建筑模型制作师"}, {"code": 6666600738, "positionType": 4, "level": 4, "name": "建筑防水工程师"}, {"code": 6666600757, "positionType": 4, "level": 4, "name": "建筑工程技术总工"}, {"code": 6666600758, "positionType": 4, "level": 4, "name": "建筑结构检测工程师"}, {"code": 6666600872, "positionType": 4, "level": 4, "name": "装配式建筑工程师"}], "positionType": 3, "level": 3, "name": "建筑工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220213, "subLevelModelList": [{"code": 6666600642, "positionType": 4, "level": 4, "name": "电力工程设计师"}], "positionType": 3, "level": 3, "name": "弱电工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220223, "subLevelModelList": [{"code": 6666600308, "positionType": 4, "level": 4, "name": "机电一体化工程师"}, {"code": 6666600816, "positionType": 4, "level": 4, "name": "建筑智能化调试员"}, {"code": 6666600819, "positionType": 4, "level": 4, "name": "隧道工程师"}, {"code": 6666601089, "positionType": 4, "level": 4, "name": "建筑机械租赁业务总监"}], "positionType": 3, "level": 3, "name": "建筑机电工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220214, "subLevelModelList": [{"code": 6666600294, "positionType": 4, "level": 4, "name": "给排水工程师"}], "positionType": 3, "level": 3, "name": "给排水工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220215, "subLevelModelList": [{"code": 6666600506, "positionType": 4, "level": 4, "name": "暖通工程师"}], "positionType": 3, "level": 3, "name": "暖通工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220216, "subLevelModelList": [{"code": 6666600204, "positionType": 4, "level": 4, "name": "幕墙安装工"}, {"code": 6666600787, "positionType": 4, "level": 4, "name": "幕墙工程师"}], "positionType": 3, "level": 3, "name": "幕墙工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220221, "subLevelModelList": [{"code": 6666600766, "positionType": 4, "level": 4, "name": "BIM工程师"}], "positionType": 3, "level": 3, "name": "BIM工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220224, "subLevelModelList": [{"code": 6666600081, "positionType": 4, "level": 4, "name": "消防设备技术员"}, {"code": 6666600183, "positionType": 4, "level": 4, "name": "消防工程师"}], "positionType": 3, "level": 3, "name": "消防工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 301004, "subLevelModelList": [{"code": 6666600768, "positionType": 4, "level": 4, "name": "管道储运工程师"}], "positionType": 3, "level": 3, "name": "水利工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220207, "subLevelModelList": [{"code": 6666600108, "positionType": 4, "level": 4, "name": "产业地产规划师"}, {"code": 6666600134, "positionType": 4, "level": 4, "name": "城乡规划编制工程师"}], "positionType": 3, "level": 3, "name": "城乡规划设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "建筑/规划设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000890, "subLevelModelList": [{"code": 220103, "positionType": 3, "level": 3, "name": "地产招投标", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220102, "positionType": 3, "level": 3, "name": "地产项目管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220101, "positionType": 3, "level": 3, "name": "房地产策划", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220302, "subLevelModelList": [{"code": 6666600730, "positionType": 4, "level": 4, "name": "房产经纪人"}, {"code": 6666601007, "positionType": 4, "level": 4, "name": "租赁设备残值评估师"}], "positionType": 3, "level": 3, "name": "房产评估师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "房地产规划开发", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000920, "subLevelModelList": [{"code": 220501, "subLevelModelList": [{"code": 6666600587, "positionType": 4, "level": 4, "name": "房地产战略咨询总监"}], "positionType": 3, "level": 3, "name": "地产项目总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220502, "positionType": 3, "level": 3, "name": "地产策划总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 220503, "positionType": 3, "level": 3, "name": "地产招投标总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "高端房地产职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600442, "subLevelModelList": [{"code": 160401, "subLevelModelList": [{"code": 6666600552, "positionType": 4, "level": 4, "name": "业务顾问"}, {"code": 6666600813, "positionType": 4, "level": 4, "name": "销售业务发展总监"}, {"code": 6666600953, "positionType": 4, "level": 4, "name": "房地产营销策划经理"}, {"code": 6666600999, "positionType": 4, "level": 4, "name": "二手房买卖顾问"}, {"code": 6666601095, "positionType": 4, "level": 4, "name": "房地产经纪咨询顾问"}], "positionType": 3, "level": 3, "name": "置业顾问", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 160403, "subLevelModelList": [{"code": 6666600486, "positionType": 4, "level": 4, "name": "住房租赁专员"}, {"code": 6666600519, "positionType": 4, "level": 4, "name": "房屋租赁经纪人"}, {"code": 6666600562, "positionType": 4, "level": 4, "name": "房地产经纪人"}, {"code": 6666601036, "positionType": 4, "level": 4, "name": "租赁业务拓展专员"}, {"code": 6666601076, "positionType": 4, "level": 4, "name": "房屋租赁专员"}], "positionType": 3, "level": 3, "name": "地产中介", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "房地产销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001280, "subLevelModelList": [{"code": 300606, "subLevelModelList": [{"code": 6666601018, "positionType": 4, "level": 4, "name": "电工"}], "positionType": 3, "level": 3, "name": "电工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300604, "subLevelModelList": [{"code": 6666600529, "positionType": 4, "level": 4, "name": "电焊工"}, {"code": 6666600874, "positionType": 4, "level": 4, "name": "机械零件加工技师"}], "positionType": 3, "level": 3, "name": "焊工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300608, "subLevelModelList": [{"code": 6666600170, "positionType": 4, "level": 4, "name": "精细木工"}, {"code": 6666600507, "positionType": 4, "level": 4, "name": "木工"}, {"code": 6666601082, "positionType": 4, "level": 4, "name": "木工（模板工）"}], "positionType": 3, "level": 3, "name": "木工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300625, "positionType": 3, "level": 3, "name": "空调工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300609, "subLevelModelList": [{"code": 6666600517, "positionType": 4, "level": 4, "name": "油漆工"}], "positionType": 3, "level": 3, "name": "油漆工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300626, "subLevelModelList": [{"code": 6666600828, "positionType": 4, "level": 4, "name": "电梯安装工"}], "positionType": 3, "level": 3, "name": "电梯工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300639, "subLevelModelList": [{"code": 6666600013, "positionType": 4, "level": 4, "name": "砌筑工"}, {"code": 6666600231, "positionType": 4, "level": 4, "name": "沥青工"}, {"code": 6666600341, "positionType": 4, "level": 4, "name": "抹灰工"}], "positionType": 3, "level": 3, "name": "泥瓦工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300638, "subLevelModelList": [{"code": 6666600076, "positionType": 4, "level": 4, "name": "水井工"}, {"code": 6666600604, "positionType": 4, "level": 4, "name": "供水管道工"}, {"code": 6666600621, "positionType": 4, "level": 4, "name": "供水仪表工"}, {"code": 6666600634, "positionType": 4, "level": 4, "name": "供水设备电工"}], "positionType": 3, "level": 3, "name": "水电工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "建筑/装修工人", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000930, "subLevelModelList": [{"code": 220601, "subLevelModelList": [{"code": 6666600657, "positionType": 4, "level": 4, "name": "住宅产品定位顾问"}, {"code": 6666600677, "positionType": 4, "level": 4, "name": "房地产数据可视化工程师"}], "positionType": 3, "level": 3, "name": "其他房地产职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他房地产职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "房地产/建筑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1080000, "subLevelModelList": [{"code": 2600872, "subLevelModelList": [{"code": 170610, "subLevelModelList": [{"code": 6666600994, "positionType": 4, "level": 4, "name": "直播运营专员"}], "positionType": 3, "level": 3, "name": "主播", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170625, "subLevelModelList": [{"code": 6666600256, "positionType": 4, "level": 4, "name": "直播销售主播"}, {"code": 6666600650, "positionType": 4, "level": 4, "name": "直播主播（农副产品）"}, {"code": 6666600978, "positionType": 4, "level": 4, "name": "调味品直播运营专员"}], "positionType": 3, "level": 3, "name": "带货主播", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130122, "subLevelModelList": [{"code": 6666600166, "positionType": 4, "level": 4, "name": "直播运营专员（食品带货）"}, {"code": 6666600809, "positionType": 4, "level": 4, "name": "教育私域流量运营主管"}, {"code": 6666601070, "positionType": 4, "level": 4, "name": "新媒体运营专员"}], "positionType": 3, "level": 3, "name": "直播运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170629, "positionType": 3, "level": 3, "name": "游戏主播", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170621, "positionType": 3, "level": 3, "name": "中控/场控/助播", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "直播", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000570, "subLevelModelList": [{"code": 210609, "positionType": 3, "level": 3, "name": "化妆/造型/服装", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170611, "subLevelModelList": [{"code": 6666600531, "positionType": 4, "level": 4, "name": "演艺人员 / 舞台监督"}], "positionType": 3, "level": 3, "name": "演员/配音员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170617, "positionType": 3, "level": 3, "name": "艺人助理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170620, "positionType": 3, "level": 3, "name": "主持人/DJ", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170630, "positionType": 3, "level": 3, "name": "模特", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170602, "positionType": 3, "level": 3, "name": "摄影/摄像师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170601, "positionType": 3, "level": 3, "name": "导演/编导", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170605, "subLevelModelList": [{"code": 6666600556, "positionType": 4, "level": 4, "name": "艺人经纪专员"}], "positionType": 3, "level": 3, "name": "经纪人/星探", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170616, "positionType": 3, "level": 3, "name": "编剧", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170615, "positionType": 3, "level": 3, "name": "制片人", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170609, "subLevelModelList": [{"code": 6666601026, "positionType": 4, "level": 4, "name": "影视项目策划经理"}], "positionType": 3, "level": 3, "name": "影视策划", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170608, "positionType": 3, "level": 3, "name": "影视发行", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170603, "subLevelModelList": [{"code": 6666600002, "positionType": 4, "level": 4, "name": "销售短视频营销专员"}, {"code": 6666601052, "positionType": 4, "level": 4, "name": "教育视频剪辑师"}], "positionType": 3, "level": 3, "name": "视频剪辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170606, "positionType": 3, "level": 3, "name": "后期制作", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 120120, "positionType": 3, "level": 3, "name": "动画设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 120123, "positionType": 3, "level": 3, "name": "修图师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170622, "positionType": 3, "level": 3, "name": "灯光师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170614, "positionType": 3, "level": 3, "name": "录音/音效", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170604, "positionType": 3, "level": 3, "name": "音频编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170624, "positionType": 3, "level": 3, "name": "影视特效", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170626, "positionType": 3, "level": 3, "name": "剧本杀主持人", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170628, "subLevelModelList": [{"code": 6666600741, "positionType": 4, "level": 4, "name": "儿童安全督导员"}], "positionType": 3, "level": 3, "name": "儿童引导师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170613, "positionType": 3, "level": 3, "name": "放映员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170627, "positionType": 3, "level": 3, "name": "剧本杀编剧", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 120614, "subLevelModelList": [{"code": 6666600312, "positionType": 4, "level": 4, "name": "舞台设计师"}], "positionType": 3, "level": 3, "name": "舞美设计师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "影视", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000560, "subLevelModelList": [{"code": 140604, "subLevelModelList": [{"code": 6666600091, "positionType": 4, "level": 4, "name": "销售活动策划经理"}, {"code": 6666600557, "positionType": 4, "level": 4, "name": "企业营销策划经理"}, {"code": 6666600752, "positionType": 4, "level": 4, "name": "数字化营销策划经理"}, {"code": 6666601035, "positionType": 4, "level": 4, "name": "文艺演出策划师"}], "positionType": 3, "level": 3, "name": "策划经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140605, "positionType": 3, "level": 3, "name": "广告文案", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140202, "positionType": 3, "level": 3, "name": "广告客户执行", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140601, "subLevelModelList": [{"code": 6666600356, "positionType": 4, "level": 4, "name": "销售文案编辑专员"}, {"code": 6666600420, "positionType": 4, "level": 4, "name": "广告策划员"}, {"code": 6666600441, "positionType": 4, "level": 4, "name": "销售文案策划专员"}], "positionType": 3, "level": 3, "name": "广告创意策划", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140407, "subLevelModelList": [{"code": 6666600747, "positionType": 4, "level": 4, "name": "数字营销策划总监"}], "positionType": 3, "level": 3, "name": "创意总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140603, "subLevelModelList": [{"code": 6666600278, "positionType": 4, "level": 4, "name": "图文设计师"}, {"code": 6666600386, "positionType": 4, "level": 4, "name": "广告设计"}, {"code": 6666600732, "positionType": 4, "level": 4, "name": "广告设计师"}, {"code": 6666600839, "positionType": 4, "level": 4, "name": "广告设计专员"}], "positionType": 3, "level": 3, "name": "广告设计", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140602, "positionType": 3, "level": 3, "name": "美术指导", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140607, "positionType": 3, "level": 3, "name": "广告制作", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140611, "positionType": 3, "level": 3, "name": "广告审核", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170212, "subLevelModelList": [{"code": 6666600603, "positionType": 4, "level": 4, "name": "文化策展项目经理"}, {"code": 6666600626, "positionType": 4, "level": 4, "name": "品牌推广经理"}, {"code": 6666600840, "positionType": 4, "level": 4, "name": "广告策划专员"}], "positionType": 3, "level": 3, "name": "广告/会展项目经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "广告", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000540, "subLevelModelList": [{"code": 130203, "positionType": 3, "level": 3, "name": "文案编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170104, "positionType": 3, "level": 3, "name": "作者/撰稿人", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170102, "positionType": 3, "level": 3, "name": "编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170109, "positionType": 3, "level": 3, "name": "印刷排版", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170106, "positionType": 3, "level": 3, "name": "校对录入", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170101, "positionType": 3, "level": 3, "name": "记者/采编", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130204, "positionType": 3, "level": 3, "name": "网站编辑", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130201, "positionType": 3, "level": 3, "name": "主编/副主编", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170105, "positionType": 3, "level": 3, "name": "出版发行", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170627, "positionType": 3, "level": 3, "name": "剧本杀编剧", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "采编/写作/出版", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000580, "subLevelModelList": [{"code": 170501, "positionType": 3, "level": 3, "name": "其他传媒职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他传媒职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "直播/影视/传媒", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1050000, "subLevelModelList": [{"code": 1000310, "subLevelModelList": [{"code": 140101, "subLevelModelList": [{"code": 6666600118, "positionType": 4, "level": 4, "name": "营销策划专员"}, {"code": 6666600133, "positionType": 4, "level": 4, "name": "品牌营销策划经理"}, {"code": 6666600273, "positionType": 4, "level": 4, "name": "市场策划专员"}, {"code": 6666600320, "positionType": 4, "level": 4, "name": "销售活动策划专员"}, {"code": 6666600416, "positionType": 4, "level": 4, "name": "教育研学旅行策划师"}, {"code": 6666600928, "positionType": 4, "level": 4, "name": "市场营销专员"}, {"code": 6666600979, "positionType": 4, "level": 4, "name": "市场调研员"}, {"code": 6666601031, "positionType": 4, "level": 4, "name": "营销经理"}], "positionType": 3, "level": 3, "name": "市场营销策划", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140111, "positionType": 3, "level": 3, "name": "海外市场", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140109, "subLevelModelList": [{"code": 6666600106, "positionType": 4, "level": 4, "name": "销售活动执行专员"}, {"code": 6666600309, "positionType": 4, "level": 4, "name": "科普活动策划"}], "positionType": 3, "level": 3, "name": "活动策划执行", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140506, "positionType": 3, "level": 3, "name": "会务/会展执行", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140505, "subLevelModelList": [{"code": 6666600305, "positionType": 4, "level": 4, "name": "展览展示策划"}], "positionType": 3, "level": 3, "name": "会务/会展策划", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140401, "subLevelModelList": [{"code": 6666600637, "positionType": 4, "level": 4, "name": "品牌总监"}, {"code": 6666600761, "positionType": 4, "level": 4, "name": "市场总监"}], "positionType": 3, "level": 3, "name": "市场总监", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140404, "positionType": 3, "level": 3, "name": "CMO", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "市场营销", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600952, "subLevelModelList": [{"code": 140104, "subLevelModelList": [{"code": 6666600182, "positionType": 4, "level": 4, "name": "调味品品牌推广专员"}, {"code": 6666600207, "positionType": 4, "level": 4, "name": "销售市场推广经理"}, {"code": 6666600277, "positionType": 4, "level": 4, "name": "技术推广专员"}, {"code": 6666600403, "positionType": 4, "level": 4, "name": "市场推广专员"}, {"code": 6666600826, "positionType": 4, "level": 4, "name": "轻质建材推广专员"}], "positionType": 3, "level": 3, "name": "市场推广/地推", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 130109, "subLevelModelList": [{"code": 6666600920, "positionType": 4, "level": 4, "name": "销售网络推广专员"}], "positionType": 3, "level": 3, "name": "网络推广", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140115, "positionType": 3, "level": 3, "name": "游戏推广", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140608, "positionType": 3, "level": 3, "name": "媒介投放", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140609, "positionType": 3, "level": 3, "name": "媒介商务BD", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140204, "positionType": 3, "level": 3, "name": "媒介专员", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140206, "subLevelModelList": [{"code": 6666600864, "positionType": 4, "level": 4, "name": "品牌经理"}], "positionType": 3, "level": 3, "name": "媒介经理/总监", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140116, "positionType": 3, "level": 3, "name": "信息流优化师", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140105, "positionType": 3, "level": 3, "name": "SEO", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140106, "positionType": 3, "level": 3, "name": "SEM", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "推广/投放", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000300, "subLevelModelList": [{"code": 140112, "positionType": 3, "level": 3, "name": "政府关系", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140804, "subLevelModelList": [{"code": 6666600384, "positionType": 4, "level": 4, "name": "项目申报专员"}, {"code": 6666600777, "positionType": 4, "level": 4, "name": "项目预算员"}], "positionType": 3, "level": 3, "name": "项目申报专员", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140801, "positionType": 3, "level": 3, "name": "政策研究", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140803, "positionType": 3, "level": 3, "name": "社工", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "政府事务", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000320, "subLevelModelList": [{"code": 140203, "subLevelModelList": [{"code": 6666600982, "positionType": 4, "level": 4, "name": "品牌推广与营销策划专员"}, {"code": 6666600987, "positionType": 4, "level": 4, "name": "品牌策划专员"}, {"code": 6666601053, "positionType": 4, "level": 4, "name": "品牌专员"}], "positionType": 3, "level": 3, "name": "品牌公关", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140405, "subLevelModelList": [{"code": 6666600647, "positionType": 4, "level": 4, "name": "客户总监"}], "positionType": 3, "level": 3, "name": "公关总监", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "公关", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000340, "subLevelModelList": [{"code": 140604, "subLevelModelList": [{"code": 6666600091, "positionType": 4, "level": 4, "name": "销售活动策划经理"}, {"code": 6666600557, "positionType": 4, "level": 4, "name": "企业营销策划经理"}, {"code": 6666600752, "positionType": 4, "level": 4, "name": "数字化营销策划经理"}, {"code": 6666601035, "positionType": 4, "level": 4, "name": "文艺演出策划师"}], "positionType": 3, "level": 3, "name": "策划经理", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140605, "positionType": 3, "level": 3, "name": "广告文案", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140202, "positionType": 3, "level": 3, "name": "广告客户执行", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140601, "subLevelModelList": [{"code": 6666600356, "positionType": 4, "level": 4, "name": "销售文案编辑专员"}, {"code": 6666600420, "positionType": 4, "level": 4, "name": "广告策划员"}, {"code": 6666600441, "positionType": 4, "level": 4, "name": "销售文案策划专员"}], "positionType": 3, "level": 3, "name": "广告创意策划", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140407, "subLevelModelList": [{"code": 6666600747, "positionType": 4, "level": 4, "name": "数字营销策划总监"}], "positionType": 3, "level": 3, "name": "创意总监", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140603, "subLevelModelList": [{"code": 6666600278, "positionType": 4, "level": 4, "name": "图文设计师"}, {"code": 6666600386, "positionType": 4, "level": 4, "name": "广告设计"}, {"code": 6666600732, "positionType": 4, "level": 4, "name": "广告设计师"}, {"code": 6666600839, "positionType": 4, "level": 4, "name": "广告设计专员"}], "positionType": 3, "level": 3, "name": "广告设计", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140602, "positionType": 3, "level": 3, "name": "美术指导", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140607, "positionType": 3, "level": 3, "name": "广告制作", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140611, "positionType": 3, "level": 3, "name": "广告审核", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 170212, "subLevelModelList": [{"code": 6666600603, "positionType": 4, "level": 4, "name": "文化策展项目经理"}, {"code": 6666600626, "positionType": 4, "level": 4, "name": "品牌推广经理"}, {"code": 6666600840, "positionType": 4, "level": 4, "name": "广告策划专员"}], "positionType": 3, "level": 3, "name": "广告/会展项目经理", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "广告", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1001240, "subLevelModelList": [{"code": 260109, "subLevelModelList": [{"code": 6666600530, "positionType": 4, "level": 4, "name": "市场调研专员"}, {"code": 6666600975, "positionType": 4, "level": 4, "name": "市场拓展专员"}, {"code": 6666601045, "positionType": 4, "level": 4, "name": "销售市场调研主管"}], "positionType": 3, "level": 3, "name": "市场调研", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140114, "positionType": 3, "level": 3, "name": "选址开发", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140108, "subLevelModelList": [{"code": 6666601044, "positionType": 4, "level": 4, "name": "渠道数据分析员（食品调味品）"}], "positionType": 3, "level": 3, "name": "商业数据分析", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "调研分析", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600462, "subLevelModelList": [{"code": 140313, "subLevelModelList": [{"code": 6666600010, "positionType": 4, "level": 4, "name": "智能建筑系统销售代表"}], "positionType": 3, "level": 3, "name": "广告销售", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140504, "subLevelModelList": [{"code": 6666600247, "positionType": 4, "level": 4, "name": "会展销售顾问"}, {"code": 6666600888, "positionType": 4, "level": 4, "name": "会议及展览服务专员"}], "positionType": 3, "level": 3, "name": "会展活动销售", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140501, "positionType": 3, "level": 3, "name": "会议活动销售", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "广告/会展销售", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000360, "subLevelModelList": [{"code": 140701, "positionType": 3, "level": 3, "name": "其他市场职位", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他市场职位", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "市场/公关/广告", "searchKey": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1130000, "subLevelModelList": [{"code": 1000840, "subLevelModelList": [{"code": 240103, "subLevelModelList": [{"code": 6666600339, "positionType": 4, "level": 4, "name": "调味品物流专员"}, {"code": 6666600453, "positionType": 4, "level": 4, "name": "冷链物流专员"}, {"code": 6666600691, "positionType": 4, "level": 4, "name": "应急备件物流专员"}, {"code": 6666600865, "positionType": 4, "level": 4, "name": "物流数据分析员"}, {"code": 6666600968, "positionType": 4, "level": 4, "name": "农产品供应链专员（含冷链）"}, {"code": 6666600995, "positionType": 4, "level": 4, "name": "物流专员"}], "positionType": 3, "level": 3, "name": "物流专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240104, "subLevelModelList": [{"code": 6666600421, "positionType": 4, "level": 4, "name": "物流主管"}, {"code": 6666600641, "positionType": 4, "level": 4, "name": "物流经理"}, {"code": 6666601022, "positionType": 4, "level": 4, "name": "冷链物流主管"}], "positionType": 3, "level": 3, "name": "物流经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240402, "positionType": 3, "level": 3, "name": "物流总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240105, "positionType": 3, "level": 3, "name": "物流运营", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240106, "subLevelModelList": [{"code": 6666600442, "positionType": 4, "level": 4, "name": "物流信息管理员"}, {"code": 6666600706, "positionType": 4, "level": 4, "name": "货运跟单员"}], "positionType": 3, "level": 3, "name": "物流跟单", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240108, "subLevelModelList": [{"code": 6666600069, "positionType": 4, "level": 4, "name": "货运调度专员"}, {"code": 6666600171, "positionType": 4, "level": 4, "name": "货运调度员"}, {"code": 6666600546, "positionType": 4, "level": 4, "name": "农产品物流调度员"}, {"code": 6666600932, "positionType": 4, "level": 4, "name": "物流调度员"}], "positionType": 3, "level": 3, "name": "调度员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240110, "subLevelModelList": [{"code": 6666600743, "positionType": 4, "level": 4, "name": "车辆调度主管"}, {"code": 6666600751, "positionType": 4, "level": 4, "name": "重型设备运输经理"}], "positionType": 3, "level": 3, "name": "运输经理/主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240119, "subLevelModelList": [{"code": 6666600500, "positionType": 4, "level": 4, "name": "冷链运输专员"}], "positionType": 3, "level": 3, "name": "跟车员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240113, "positionType": 3, "level": 3, "name": "水/空/陆运操作", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240111, "positionType": 3, "level": 3, "name": "货代/物流销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240109, "subLevelModelList": [{"code": 6666600023, "positionType": 4, "level": 4, "name": "智能仓储规划师"}, {"code": 6666600156, "positionType": 4, "level": 4, "name": "仓储主管（农副产品）"}, {"code": 6666600376, "positionType": 4, "level": 4, "name": "仓储主管"}], "positionType": 3, "level": 3, "name": "物流/仓储项目经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240302, "positionType": 3, "level": 3, "name": "集装箱管理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "物流/运输", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600742, "subLevelModelList": [{"code": 240206, "subLevelModelList": [{"code": 6666600559, "positionType": 4, "level": 4, "name": "仓库配货员"}], "positionType": 3, "level": 3, "name": "配/理/拣/发货", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240303, "positionType": 3, "level": 3, "name": "配送员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 290205, "positionType": 3, "level": 3, "name": "送餐员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240304, "positionType": 3, "level": 3, "name": "快递员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300630, "positionType": 3, "level": 3, "name": "搬运工/装卸工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300602, "subLevelModelList": [{"code": 6666600450, "positionType": 4, "level": 4, "name": "起重工"}, {"code": 6666600455, "positionType": 4, "level": 4, "name": "叉车操作员"}], "positionType": 3, "level": 3, "name": "叉车工", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240118, "positionType": 3, "level": 3, "name": "配送站长", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "配送理货", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000860, "subLevelModelList": [{"code": 240301, "subLevelModelList": [{"code": 6666600401, "positionType": 4, "level": 4, "name": "货运业务员"}, {"code": 6666600951, "positionType": 4, "level": 4, "name": "货运车辆自动驾驶测试员"}, {"code": 6666600998, "positionType": 4, "level": 4, "name": "货运客服专员"}, {"code": 6666601020, "positionType": 4, "level": 4, "name": "货运安检员"}, {"code": 6666601092, "positionType": 4, "level": 4, "name": "货车司机"}], "positionType": 3, "level": 3, "name": "货运司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 150208, "positionType": 3, "level": 3, "name": "商务司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240305, "positionType": 3, "level": 3, "name": "网约车司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240306, "positionType": 3, "level": 3, "name": "代驾司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240307, "positionType": 3, "level": 3, "name": "驾校教练", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240308, "positionType": 3, "level": 3, "name": "客运司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 100311, "subLevelModelList": [{"code": 6666600055, "positionType": 4, "level": 4, "name": "机手培训教官"}, {"code": 6666600126, "positionType": 4, "level": 4, "name": "无人机测绘员"}], "positionType": 3, "level": 3, "name": "无人机飞手", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "驾驶员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000850, "subLevelModelList": [{"code": 240204, "subLevelModelList": [{"code": 6666600090, "positionType": 4, "level": 4, "name": "仓库管理员​"}, {"code": 6666600135, "positionType": 4, "level": 4, "name": "食品仓储管理员"}, {"code": 6666600275, "positionType": 4, "level": 4, "name": "仓库管理员"}, {"code": 6666600429, "positionType": 4, "level": 4, "name": "库存管理员"}, {"code": 6666600437, "positionType": 4, "level": 4, "name": "仓储管理员​"}, {"code": 6666600796, "positionType": 4, "level": 4, "name": "零部件仓储管理员"}], "positionType": 3, "level": 3, "name": "仓库管理员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240205, "positionType": 3, "level": 3, "name": "仓库文员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240201, "subLevelModelList": [{"code": 6666600035, "positionType": 4, "level": 4, "name": "仓储主管（常温 / 冷链）"}, {"code": 6666600045, "positionType": 4, "level": 4, "name": "仓储物流主管"}, {"code": 6666600200, "positionType": 4, "level": 4, "name": "仓储管理员"}, {"code": 6666600300, "positionType": 4, "level": 4, "name": "食品仓储主管（常温 + 冷链）"}, {"code": 6666600497, "positionType": 4, "level": 4, "name": "仓库主管"}, {"code": 6666600648, "positionType": 4, "level": 4, "name": "仓库物流主管"}, {"code": 6666600681, "positionType": 4, "level": 4, "name": "仓储主管（多品类管理）"}], "positionType": 3, "level": 3, "name": "仓库主管/经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "仓储", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000870, "subLevelModelList": [{"code": 240101, "subLevelModelList": [{"code": 6666600092, "positionType": 4, "level": 4, "name": "冷冻食品供应链专员"}, {"code": 6666600445, "positionType": 4, "level": 4, "name": "食品原料溯源专员"}, {"code": 6666600852, "positionType": 4, "level": 4, "name": "供应链专员"}], "positionType": 3, "level": 3, "name": "供应链专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240102, "subLevelModelList": [{"code": 6666600031, "positionType": 4, "level": 4, "name": "供应链工程师（模具材料）"}, {"code": 6666600088, "positionType": 4, "level": 4, "name": "供应链经理"}, {"code": 6666600281, "positionType": 4, "level": 4, "name": "劳保用品供应链经理"}, {"code": 6666600347, "positionType": 4, "level": 4, "name": "五金供应链经理"}, {"code": 6666600488, "positionType": 4, "level": 4, "name": "备件供应链经理"}, {"code": 6666600676, "positionType": 4, "level": 4, "name": "建筑销售供应链经理"}], "positionType": 3, "level": 3, "name": "供应链经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240401, "subLevelModelList": [{"code": 6666600196, "positionType": 4, "level": 4, "name": "食品调味品供应链总监"}, {"code": 6666600259, "positionType": 4, "level": 4, "name": " 供应链总监"}, {"code": 6666600801, "positionType": 4, "level": 4, "name": "供应链管理总监"}], "positionType": 3, "level": 3, "name": "供应链总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 300107, "subLevelModelList": [{"code": 6666600078, "positionType": 4, "level": 4, "name": "生产计划主管"}, {"code": 6666600192, "positionType": 4, "level": 4, "name": "生产计划员（电子产品）"}, {"code": 6666600575, "positionType": 4, "level": 4, "name": "3D打印设备操作员"}, {"code": 6666600690, "positionType": 4, "level": 4, "name": "3D打印生产计划员"}], "positionType": 3, "level": 3, "name": "生产计划/物料管理(PMC)", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "供应链", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "物流/仓储/司机", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1120000, "subLevelModelList": [{"code": 1000810, "subLevelModelList": [{"code": 250103, "subLevelModelList": [{"code": 6666600005, "positionType": 4, "level": 4, "name": "货运车辆采购专员"}, {"code": 6666600006, "positionType": 4, "level": 4, "name": "文具采购专员"}, {"code": 6666600049, "positionType": 4, "level": 4, "name": "生产资料采购专员"}, {"code": 6666600110, "positionType": 4, "level": 4, "name": "包装材料采购专员"}, {"code": 6666600137, "positionType": 4, "level": 4, "name": "建筑装饰材料采购专员"}, {"code": 6666600194, "positionType": 4, "level": 4, "name": "劳保及防水材料采购专员"}, {"code": 6666600282, "positionType": 4, "level": 4, "name": "食品选品专员（线上渠道）"}, {"code": 6666600369, "positionType": 4, "level": 4, "name": "办公用品采购专员"}, {"code": 6666600396, "positionType": 4, "level": 4, "name": "采购员"}, {"code": 6666600570, "positionType": 4, "level": 4, "name": "文化用品渠道专员"}, {"code": 6666600646, "positionType": 4, "level": 4, "name": "采购专员（机械配件）"}, {"code": 6666600684, "positionType": 4, "level": 4, "name": "金属材料采购专员"}, {"code": 6666600708, "positionType": 4, "level": 4, "name": "机械配件采购主管"}, {"code": 6666600713, "positionType": 4, "level": 4, "name": "机械设备采购专员"}, {"code": 6666600720, "positionType": 4, "level": 4, "name": "采购助理"}, {"code": 6666600790, "positionType": 4, "level": 4, "name": "采购专员​"}, {"code": 6666600811, "positionType": 4, "level": 4, "name": "采购专员（五金供应链）"}, {"code": 6666600829, "positionType": 4, "level": 4, "name": "工程机械配件采购专员"}, {"code": 6666600974, "positionType": 4, "level": 4, "name": "采购专员"}, {"code": 6666600976, "positionType": 4, "level": 4, "name": "出版物采购专员"}, {"code": 6666601038, "positionType": 4, "level": 4, "name": "调味品采购助理"}, {"code": 6666601079, "positionType": 4, "level": 4, "name": "材料采购员"}], "positionType": 3, "level": 3, "name": "采购专员/助理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250102, "subLevelModelList": [{"code": 6666600233, "positionType": 4, "level": 4, "name": "采购经理​"}, {"code": 6666600512, "positionType": 4, "level": 4, "name": "食品采购经理（预包装 + 农产品）"}, {"code": 6666600544, "positionType": 4, "level": 4, "name": "采购主管"}, {"code": 6666600798, "positionType": 4, "level": 4, "name": "采购经理"}, {"code": 6666600885, "positionType": 4, "level": 4, "name": "采购主管（原料）"}, {"code": 6666600990, "positionType": 4, "level": 4, "name": "农副产品采购经理（坚果 / 干果）"}, {"code": 6666601099, "positionType": 4, "level": 4, "name": "原料采购经理（粮油 / 肉类）"}], "positionType": 3, "level": 3, "name": "采购经理/主管", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250105, "subLevelModelList": [{"code": 6666600363, "positionType": 4, "level": 4, "name": "采购工程师（零部件）"}, {"code": 6666600806, "positionType": 4, "level": 4, "name": "采购工程师（配件）"}], "positionType": 3, "level": 3, "name": "采购工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250104, "positionType": 3, "level": 3, "name": "买手", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250108, "subLevelModelList": [{"code": 6666600383, "positionType": 4, "level": 4, "name": "质量工程师（模具 / 电子）"}, {"code": 6666600584, "positionType": 4, "level": 4, "name": "质量控制经理"}, {"code": 6666600649, "positionType": 4, "level": 4, "name": "技术支持工程师"}, {"code": 6666600849, "positionType": 4, "level": 4, "name": "质量工程师"}], "positionType": 3, "level": 3, "name": "供应商质量工程师", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250111, "subLevelModelList": [{"code": 6666600744, "positionType": 4, "level": 4, "name": "冷冻食品品控专员"}, {"code": 6666600853, "positionType": 4, "level": 4, "name": "商品专员"}], "positionType": 3, "level": 3, "name": "商品专员/助理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 140312, "subLevelModelList": [{"code": 6666600721, "positionType": 4, "level": 4, "name": "建筑设备租赁经理"}, {"code": 6666600854, "positionType": 4, "level": 4, "name": "调味品产品总监"}], "positionType": 3, "level": 3, "name": "商品经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250101, "subLevelModelList": [{"code": 6666600674, "positionType": 4, "level": 4, "name": "采购总监"}], "positionType": 3, "level": 3, "name": "采购总监", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250109, "subLevelModelList": [{"code": 6666600100, "positionType": 4, "level": 4, "name": "招标专员"}, {"code": 6666601071, "positionType": 4, "level": 4, "name": "工程招投标分析师"}], "positionType": 3, "level": 3, "name": "招标专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250110, "subLevelModelList": [{"code": 6666600198, "positionType": 4, "level": 4, "name": "销售投标主管"}, {"code": 6666600333, "positionType": 4, "level": 4, "name": "投标专员"}], "positionType": 3, "level": 3, "name": "投标专员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "采购", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2600482, "subLevelModelList": [{"code": 250203, "subLevelModelList": [{"code": 6666600481, "positionType": 4, "level": 4, "name": "外贸业务员"}], "positionType": 3, "level": 3, "name": "外贸业务员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250201, "subLevelModelList": [{"code": 6666600448, "positionType": 4, "level": 4, "name": "五金外贸经理"}], "positionType": 3, "level": 3, "name": "外贸经理", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 250205, "positionType": 3, "level": 3, "name": "海外销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "外贸销售", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000820, "subLevelModelList": [{"code": 250204, "positionType": 3, "level": 3, "name": "贸易跟单", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240117, "positionType": 3, "level": 3, "name": "单证员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}, {"code": 240114, "positionType": 3, "level": 3, "name": "报关/报检员", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "进出口贸易", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1000830, "subLevelModelList": [{"code": 250301, "positionType": 3, "level": 3, "name": "其他采购/贸易类职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他采购/贸易职位", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "positionType": 0, "level": 1, "name": "采购/贸易", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2152200, "subLevelModelList": [{"code": 2600772, "subLevelModelList": [{"code": 300801, "positionType": 3, "level": 3, "name": "电池工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 300802, "positionType": 3, "level": 3, "name": "电机工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "新能源汽车", "searchKey": "qiche"}, {"code": 2600782, "subLevelModelList": [{"code": 230102, "positionType": 3, "level": 3, "name": "车身/造型设计", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230106, "positionType": 3, "level": 3, "name": "汽车电子工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 101308, "subLevelModelList": [{"code": 6666601064, "positionType": 4, "level": 4, "name": "智能驾驶系统开发工程师"}], "positionType": 3, "level": 3, "name": "自动驾驶系统工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 300803, "positionType": 3, "level": 3, "name": "线束设计", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230110, "subLevelModelList": [{"code": 6666600011, "positionType": 4, "level": 4, "name": "园林绿化工程师"}, {"code": 6666600760, "positionType": 4, "level": 4, "name": "设计工程师"}], "positionType": 3, "level": 3, "name": "内外饰设计工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230105, "positionType": 3, "level": 3, "name": "动力系统工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230103, "positionType": 3, "level": 3, "name": "底盘工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230107, "subLevelModelList": [{"code": 6666600017, "positionType": 4, "level": 4, "name": "汽车配件研发工程师"}], "positionType": 3, "level": 3, "name": "汽车零部件设计", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230101, "subLevelModelList": [{"code": 6666600008, "positionType": 4, "level": 4, "name": "汽车设计工程师"}], "positionType": 3, "level": 3, "name": "汽车设计", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230109, "positionType": 3, "level": 3, "name": "汽车质量工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230210, "positionType": 3, "level": 3, "name": "总装工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230108, "positionType": 3, "level": 3, "name": "汽车项目管理", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230111, "positionType": 3, "level": 3, "name": "总布置工程师", "searchKey": "qiche", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "汽车研发/制造", "searchKey": "qiche"}, {"code": 2600802, "subLevelModelList": [{"code": 230204, "subLevelModelList": [{"code": 6666600389, "positionType": 4, "level": 4, "name": "汽车维修技师"}, {"code": 6666600717, "positionType": 4, "level": 4, "name": "车辆维修技师"}, {"code": 6666600918, "positionType": 4, "level": 4, "name": "维修质检专员"}], "positionType": 3, "level": 3, "name": "汽车维修", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230205, "positionType": 3, "level": 3, "name": "汽车美容", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230213, "positionType": 3, "level": 3, "name": "洗车工", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230209, "positionType": 3, "level": 3, "name": "汽车改装", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230203, "subLevelModelList": [{"code": 6666600475, "positionType": 4, "level": 4, "name": "汽车租赁专员"}, {"code": 6666600755, "positionType": 4, "level": 4, "name": "汽车技术咨询顾问"}, {"code": 6666600815, "positionType": 4, "level": 4, "name": "大客户经理（汽车后市场）"}], "positionType": 3, "level": 3, "name": "汽车服务顾问", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230208, "positionType": 3, "level": 3, "name": "4S店店长/维修站长", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230207, "subLevelModelList": [{"code": 6666600218, "positionType": 4, "level": 4, "name": "二手车评估师"}, {"code": 6666600923, "positionType": 4, "level": 4, "name": "二手设备评估师"}], "positionType": 3, "level": 3, "name": "二手车评估师", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230206, "positionType": 3, "level": 3, "name": "汽车查勘定损", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230214, "positionType": 3, "level": 3, "name": "加油员", "searchKey": "qiche", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "汽车服务", "searchKey": "qiche"}, {"code": 2600792, "subLevelModelList": [{"code": 230201, "subLevelModelList": [{"code": 6666600119, "positionType": 4, "level": 4, "name": "二手车销售顾问"}, {"code": 6666600313, "positionType": 4, "level": 4, "name": "新能源汽车销售经理"}, {"code": 6666600398, "positionType": 4, "level": 4, "name": "汽车销售代表"}, {"code": 6666600509, "positionType": 4, "level": 4, "name": "汽车销售经理"}, {"code": 6666600788, "positionType": 4, "level": 4, "name": "汽车销售顾问"}, {"code": 6666600973, "positionType": 4, "level": 4, "name": "汽车销售"}, {"code": 6666601067, "positionType": 4, "level": 4, "name": "汽车零配件销售主管"}, {"code": 6666601100, "positionType": 4, "level": 4, "name": "新能源汽车整车销售顾问"}], "positionType": 3, "level": 3, "name": "汽车销售", "searchKey": "qiche", "recruitmentType": "1,2,3"}, {"code": 230202, "subLevelModelList": [{"code": 6666600292, "positionType": 4, "level": 4, "name": "汽车零配件零售顾问"}], "positionType": 3, "level": 3, "name": "汽车配件销售", "searchKey": "qiche", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "汽车销售", "searchKey": "qiche"}], "positionType": 0, "level": 1, "name": "汽车", "searchKey": "qiche"}, {"code": 1110000, "subLevelModelList": [{"code": 1000750, "subLevelModelList": [{"code": 210201, "positionType": 3, "level": 3, "name": "护士", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210503, "positionType": 3, "level": 3, "name": "导医", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210202, "positionType": 3, "level": 3, "name": "护士长", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 290111, "positionType": 3, "level": 3, "name": "护工", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "护士/护理", "searchKey": "yiliaojiankang"}, {"code": 1000740, "subLevelModelList": [{"code": 210309, "positionType": 3, "level": 3, "name": "外科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210306, "positionType": 3, "level": 3, "name": "内科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210313, "positionType": 3, "level": 3, "name": "皮肤科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210311, "positionType": 3, "level": 3, "name": "妇产科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210310, "positionType": 3, "level": 3, "name": "儿科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210312, "positionType": 3, "level": 3, "name": "眼科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210303, "positionType": 3, "level": 3, "name": "精神心理科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210402, "positionType": 3, "level": 3, "name": "整形医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210307, "subLevelModelList": [{"code": 6666600931, "positionType": 4, "level": 4, "name": "全科医生"}], "positionType": 3, "level": 3, "name": "全科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210314, "positionType": 3, "level": 3, "name": "耳鼻喉科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210111, "subLevelModelList": [{"code": 6666600878, "positionType": 4, "level": 4, "name": "医学检验师"}], "positionType": 3, "level": 3, "name": "医学检验", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210113, "positionType": 3, "level": 3, "name": "放射科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210114, "positionType": 3, "level": 3, "name": "超声科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210315, "positionType": 3, "level": 3, "name": "麻醉科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210316, "positionType": 3, "level": 3, "name": "病理科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210112, "positionType": 3, "level": 3, "name": "医生助理", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210302, "subLevelModelList": [{"code": 6666600836, "positionType": 4, "level": 4, "name": "中医师 / 坐诊医师"}], "positionType": 3, "level": 3, "name": "中医", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210304, "positionType": 3, "level": 3, "name": "口腔科医生", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210308, "positionType": 3, "level": 3, "name": "幼儿园保健医", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210104, "positionType": 3, "level": 3, "name": "药剂师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210109, "positionType": 3, "level": 3, "name": "验光师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210317, "positionType": 3, "level": 3, "name": "医务管理", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210103, "positionType": 3, "level": 3, "name": "其他医生职位", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "医生/医技", "searchKey": "yiliaojiankang"}, {"code": 1000760, "subLevelModelList": [{"code": 210403, "positionType": 3, "level": 3, "name": "理疗师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210401, "subLevelModelList": [{"code": 6666600169, "positionType": 4, "level": 4, "name": "营养师（婴幼儿食品）"}, {"code": 6666600581, "positionType": 4, "level": 4, "name": "中西医结合健康顾问"}, {"code": 6666600845, "positionType": 4, "level": 4, "name": "营养标签专员"}], "positionType": 3, "level": 3, "name": "营养师/健康管理师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210404, "positionType": 3, "level": 3, "name": "针灸推拿", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210305, "positionType": 3, "level": 3, "name": "康复治疗师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 290118, "positionType": 3, "level": 3, "name": "产后康复师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 260112, "positionType": 3, "level": 3, "name": "心理咨询师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210412, "subLevelModelList": [{"code": 6666600711, "positionType": 4, "level": 4, "name": "保健按摩师"}], "positionType": 3, "level": 3, "name": "按摩师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210411, "positionType": 3, "level": 3, "name": "足疗师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210415, "positionType": 3, "level": 3, "name": "采耳师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "理疗保健", "searchKey": "yiliaojiankang"}, {"code": 1000790, "subLevelModelList": [{"code": 210803, "positionType": 3, "level": 3, "name": "药店店员", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210802, "positionType": 3, "level": 3, "name": "执业药师/驻店药师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210801, "subLevelModelList": [{"code": 6666600061, "positionType": 4, "level": 4, "name": "药品零售门店店长"}], "positionType": 3, "level": 3, "name": "药店店长", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "药店", "searchKey": "yiliaojiankang"}, {"code": 1000770, "subLevelModelList": [{"code": 210115, "subLevelModelList": [{"code": 6666600101, "positionType": 4, "level": 4, "name": "生物医学仪器技术专家"}], "positionType": 3, "level": 3, "name": "生物学研究人员", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210108, "positionType": 3, "level": 3, "name": "医药研发", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210128, "subLevelModelList": [{"code": 6666600640, "positionType": 4, "level": 4, "name": "信息系统集成工程师"}], "positionType": 3, "level": 3, "name": "生物信息工程师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210117, "positionType": 3, "level": 3, "name": "药品生产", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210116, "positionType": 3, "level": 3, "name": "药品注册", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210123, "subLevelModelList": [{"code": 6666600352, "positionType": 4, "level": 4, "name": "药品质量管理负责人"}], "positionType": 3, "level": 3, "name": "医药项目经理", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210124, "positionType": 3, "level": 3, "name": "细胞培养员", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210130, "positionType": 3, "level": 3, "name": "药理毒理研究员", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210126, "positionType": 3, "level": 3, "name": "药物合成", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210129, "positionType": 3, "level": 3, "name": "制剂研发", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210125, "positionType": 3, "level": 3, "name": "药物分析", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210127, "subLevelModelList": [{"code": 6666600609, "positionType": 4, "level": 4, "name": "医疗资质与合规经理"}, {"code": 6666601021, "positionType": 4, "level": 4, "name": "医疗设备销售代表"}], "positionType": 3, "level": 3, "name": "医疗产品技术支持", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "生物医药", "searchKey": "yiliaojiankang"}, {"code": 1000730, "subLevelModelList": [{"code": 210119, "positionType": 3, "level": 3, "name": "临床协调员", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 211002, "positionType": 3, "level": 3, "name": "临床监查员", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 211001, "positionType": 3, "level": 3, "name": "临床项目经理", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210120, "positionType": 3, "level": 3, "name": "临床数据分析", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210118, "subLevelModelList": [{"code": 6666600964, "positionType": 4, "level": 4, "name": "医学影像技师"}], "positionType": 3, "level": 3, "name": "临床医学经理/专员", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210501, "positionType": 3, "level": 3, "name": "临床医学总监", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "临床试验", "searchKey": "yiliaojiankang"}, {"code": 1000780, "subLevelModelList": [{"code": 210122, "positionType": 3, "level": 3, "name": "医疗器械生产/质量管理", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210105, "subLevelModelList": [{"code": 6666600116, "positionType": 4, "level": 4, "name": "设备研发工程师"}, {"code": 6666600493, "positionType": 4, "level": 4, "name": "医疗器械研发工程师"}], "positionType": 3, "level": 3, "name": "医疗器械研发", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210121, "positionType": 3, "level": 3, "name": "医疗器械注册", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210901, "positionType": 3, "level": 3, "name": "试剂研发", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "医疗器械", "searchKey": "yiliaojiankang"}, {"code": 2600472, "subLevelModelList": [{"code": 210502, "positionType": 3, "level": 3, "name": "医药代表", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210506, "subLevelModelList": [{"code": 6666600177, "positionType": 4, "level": 4, "name": "医疗器械租赁专员"}, {"code": 6666600473, "positionType": 4, "level": 4, "name": "医疗器械销售总监"}, {"code": 6666600480, "positionType": 4, "level": 4, "name": "医疗器械销售经理（三类器械方向）"}], "positionType": 3, "level": 3, "name": "医疗器械销售", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210505, "subLevelModelList": [{"code": 6666600348, "positionType": 4, "level": 4, "name": "美容产品销售顾问"}], "positionType": 3, "level": 3, "name": "医美咨询", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210504, "subLevelModelList": [{"code": 6666600007, "positionType": 4, "level": 4, "name": "防腐保温材料技术顾问"}, {"code": 6666600479, "positionType": 4, "level": 4, "name": "健康咨询顾问"}, {"code": 6666600875, "positionType": 4, "level": 4, "name": "药品销售经理"}, {"code": 6666600876, "positionType": 4, "level": 4, "name": "餐饮娱乐合规顾问"}], "positionType": 3, "level": 3, "name": "健康顾问", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210507, "positionType": 3, "level": 3, "name": "口腔咨询师", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "医疗销售", "searchKey": "yiliaojiankang"}, {"code": 1000800, "subLevelModelList": [{"code": 210101, "positionType": 3, "level": 3, "name": "医学编辑", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}, {"code": 210701, "subLevelModelList": [{"code": 6666600138, "positionType": 4, "level": 4, "name": "健身休闲活动销售"}, {"code": 6666600954, "positionType": 4, "level": 4, "name": "抗疲劳测试工程师"}], "positionType": 3, "level": 3, "name": "其他医疗健康职位", "searchKey": "yiliaojiankang", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他医疗健康职位", "searchKey": "yiliaojiankang"}], "positionType": 0, "level": 1, "name": "医疗健康", "searchKey": "yiliaojiankang"}, {"code": 1090000, "subLevelModelList": [{"code": 1000610, "subLevelModelList": [{"code": 180402, "positionType": 3, "level": 3, "name": "柜员", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180404, "positionType": 3, "level": 3, "name": "银行大堂经理", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180403, "subLevelModelList": [{"code": 6666600686, "positionType": 4, "level": 4, "name": "教育客户成功经理"}], "positionType": 3, "level": 3, "name": "客户经理", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180406, "positionType": 3, "level": 3, "name": "信贷专员", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "银行", "searchKey": "jin<PERSON>"}, {"code": 1000590, "subLevelModelList": [{"code": 180106, "positionType": 3, "level": 3, "name": "证券交易员", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180802, "positionType": 3, "level": 3, "name": "卖方分析师", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180803, "positionType": 3, "level": 3, "name": "买方分析师", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180806, "positionType": 3, "level": 3, "name": "投资银行业务", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180805, "positionType": 3, "level": 3, "name": "基金经理", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180402, "positionType": 3, "level": 3, "name": "柜员", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180807, "positionType": 3, "level": 3, "name": "量化研究员", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "证券/基金/期货", "searchKey": "jin<PERSON>"}, {"code": 1000630, "subLevelModelList": [{"code": 150307, "subLevelModelList": [{"code": 6666600066, "positionType": 4, "level": 4, "name": "风险管理专员"}, {"code": 6666600422, "positionType": 4, "level": 4, "name": "租赁风控评估师"}, {"code": 6666600929, "positionType": 4, "level": 4, "name": "风控总监"}], "positionType": 3, "level": 3, "name": "风控", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180204, "positionType": 3, "level": 3, "name": "合规稽查", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180203, "positionType": 3, "level": 3, "name": "资信评估", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180304, "positionType": 3, "level": 3, "name": "清算", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180104, "positionType": 3, "level": 3, "name": "资产评估", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180501, "subLevelModelList": [{"code": **********, "positionType": 4, "level": 4, "name": "金融产品经理"}, {"code": **********, "positionType": 4, "level": 4, "name": "资产管理员"}], "positionType": 3, "level": 3, "name": "金融产品经理", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180503, "positionType": 3, "level": 3, "name": "催收员", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "中后台", "searchKey": "jin<PERSON>"}, {"code": 1000600, "subLevelModelList": [{"code": 180101, "positionType": 3, "level": 3, "name": "投资经理", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180118, "positionType": 3, "level": 3, "name": "投资助理", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180103, "positionType": 3, "level": 3, "name": "行业研究", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180115, "subLevelModelList": [{"code": 6666600227, "positionType": 4, "level": 4, "name": "融资专员"}], "positionType": 3, "level": 3, "name": "融资", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180117, "positionType": 3, "level": 3, "name": "投后管理", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180116, "positionType": 3, "level": 3, "name": "并购", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180112, "subLevelModelList": [{"code": 6666600767, "positionType": 4, "level": 4, "name": "投资总监"}], "positionType": 3, "level": 3, "name": "投资总监/VP", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180120, "positionType": 3, "level": 3, "name": "投资者关系/证券事务代表", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180111, "positionType": 3, "level": 3, "name": "其他投融资职位", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "投融资", "searchKey": "jin<PERSON>"}, {"code": 1000620, "subLevelModelList": [{"code": 180703, "subLevelModelList": [{"code": 6666601025, "positionType": 4, "level": 4, "name": "货运车辆保险理赔员"}], "positionType": 3, "level": 3, "name": "保险理赔", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180702, "positionType": 3, "level": 3, "name": "保险精算师", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "保险", "searchKey": "jin<PERSON>"}, {"code": 2600492, "subLevelModelList": [{"code": 180506, "subLevelModelList": [{"code": 6666601030, "positionType": 4, "level": 4, "name": "销售渠道拓展主管"}], "positionType": 3, "level": 3, "name": "理财顾问", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180701, "subLevelModelList": [{"code": 6666600150, "positionType": 4, "level": 4, "name": "租赁设备保险对接员"}], "positionType": 3, "level": 3, "name": "保险顾问", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180401, "positionType": 3, "level": 3, "name": "信用卡销售", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}, {"code": 180801, "positionType": 3, "level": 3, "name": "证券经纪人", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "金融销售", "searchKey": "jin<PERSON>"}, {"code": 1000650, "subLevelModelList": [{"code": 180601, "positionType": 3, "level": 3, "name": "其他金融职位", "searchKey": "jin<PERSON>", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他金融职位", "searchKey": "jin<PERSON>"}], "positionType": 0, "level": 1, "name": "金融", "searchKey": "jin<PERSON>"}, {"code": 1160000, "subLevelModelList": [{"code": 1000960, "subLevelModelList": [{"code": 260111, "subLevelModelList": [{"code": 6666600195, "positionType": 4, "level": 4, "name": "知识产权专员"}, {"code": 6666600409, "positionType": 4, "level": 4, "name": "技术转让专员"}], "positionType": 3, "level": 3, "name": "知识产权/专利/商标代理人", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 140804, "subLevelModelList": [{"code": 6666600384, "positionType": 4, "level": 4, "name": "项目申报专员"}, {"code": 6666600777, "positionType": 4, "level": 4, "name": "项目预算员"}], "positionType": 3, "level": 3, "name": "项目申报专员", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260106, "positionType": 3, "level": 3, "name": "咨询项目管理", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260101, "subLevelModelList": [{"code": 6666600471, "positionType": 4, "level": 4, "name": "企业信息化建设顾问"}], "positionType": 3, "level": 3, "name": "企业管理咨询", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260107, "subLevelModelList": [{"code": 6666600400, "positionType": 4, "level": 4, "name": "高级企业管理咨询顾问"}], "positionType": 3, "level": 3, "name": "战略咨询", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 150303, "subLevelModelList": [{"code": 6666601065, "positionType": 4, "level": 4, "name": "财务专员"}], "positionType": 3, "level": 3, "name": "财务顾问", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260104, "subLevelModelList": [{"code": 6666601042, "positionType": 4, "level": 4, "name": "信息技术咨询师"}], "positionType": 3, "level": 3, "name": "IT咨询顾问", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260402, "subLevelModelList": [{"code": 6666600038, "positionType": 4, "level": 4, "name": "房地产咨询顾问"}], "positionType": 3, "level": 3, "name": "咨询经理", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260105, "subLevelModelList": [{"code": 6666600522, "positionType": 4, "level": 4, "name": "人力资源顾问"}, {"code": 6666601024, "positionType": 4, "level": 4, "name": "招生顾问"}], "positionType": 3, "level": 3, "name": "人力资源咨询顾问", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260108, "subLevelModelList": [{"code": 6666600423, "positionType": 4, "level": 4, "name": "教育行业猎头顾问"}, {"code": 6666600630, "positionType": 4, "level": 4, "name": "招聘代理（房地产行业猎头）"}], "positionType": 3, "level": 3, "name": "猎头顾问", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260401, "subLevelModelList": [{"code": 6666600459, "positionType": 4, "level": 4, "name": "战略总监"}], "positionType": 3, "level": 3, "name": "咨询总监", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260109, "subLevelModelList": [{"code": 6666600530, "positionType": 4, "level": 4, "name": "市场调研专员"}, {"code": 6666600975, "positionType": 4, "level": 4, "name": "市场拓展专员"}, {"code": 6666601045, "positionType": 4, "level": 4, "name": "销售市场调研主管"}], "positionType": 3, "level": 3, "name": "市场调研", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 100511, "subLevelModelList": [{"code": 6666600064, "positionType": 4, "level": 4, "name": "食品销售区域分析师"}, {"code": 6666600107, "positionType": 4, "level": 4, "name": "教育舆情分析师"}, {"code": 6666600228, "positionType": 4, "level": 4, "name": "销售数据分析主管"}, {"code": 6666600232, "positionType": 4, "level": 4, "name": "房地产投资分析师"}, {"code": 6666600284, "positionType": 4, "level": 4, "name": "数据分析专员"}, {"code": 6666600374, "positionType": 4, "level": 4, "name": "建筑销售行业分析师"}, {"code": 6666600542, "positionType": 4, "level": 4, "name": " 租赁客户信用分析师"}, {"code": 6666600750, "positionType": 4, "level": 4, "name": "教育碳足迹分析师"}, {"code": 6666600771, "positionType": 4, "level": 4, "name": "市场调研分析师"}, {"code": 6666600791, "positionType": 4, "level": 4, "name": "销售数据分析专员"}, {"code": 6666601049, "positionType": 4, "level": 4, "name": "数据分析师"}], "positionType": 3, "level": 3, "name": "数据分析师", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260112, "positionType": 3, "level": 3, "name": "心理咨询师", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260113, "positionType": 3, "level": 3, "name": "婚恋咨询师", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260114, "subLevelModelList": [{"code": 6666600209, "positionType": 4, "level": 4, "name": "工程咨询师"}, {"code": 6666600343, "positionType": 4, "level": 4, "name": "咨询顾问"}], "positionType": 3, "level": 3, "name": "工程咨询", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260110, "subLevelModelList": [{"code": 6666600590, "positionType": 4, "level": 4, "name": "技术顾问"}, {"code": 6666600620, "positionType": 4, "level": 4, "name": "租赁设备增值服务顾问"}], "positionType": 3, "level": 3, "name": "其他咨询顾问", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "咨询/调研", "searchKey": "zixunfanyifalv"}, {"code": 1000980, "subLevelModelList": [{"code": 260301, "positionType": 3, "level": 3, "name": "英语翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260302, "positionType": 3, "level": 3, "name": "日语翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260303, "positionType": 3, "level": 3, "name": "韩语/朝鲜语翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260306, "positionType": 3, "level": 3, "name": "俄语翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260307, "positionType": 3, "level": 3, "name": "西班牙语翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260305, "positionType": 3, "level": 3, "name": "德语翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260304, "positionType": 3, "level": 3, "name": "法语翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260308, "positionType": 3, "level": 3, "name": "其他语种翻译", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "翻译", "searchKey": "zixunfanyifalv"}, {"code": 1000970, "subLevelModelList": [{"code": 260204, "positionType": 3, "level": 3, "name": "律师助理", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260203, "positionType": 3, "level": 3, "name": "知识产权律师", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}, {"code": 260201, "positionType": 3, "level": 3, "name": "律师", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "法律服务", "searchKey": "zixunfanyifalv"}, {"code": 1000990, "subLevelModelList": [{"code": 260501, "positionType": 3, "level": 3, "name": "其他咨询/翻译类职位", "searchKey": "zixunfanyifalv", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他咨询类职位", "searchKey": "zixunfanyifalv"}], "positionType": 0, "level": 1, "name": "咨询/翻译/法律", "searchKey": "zixunfanyifalv"}, {"code": 1150000, "subLevelModelList": [{"code": 1000940, "subLevelModelList": [{"code": 301002, "positionType": 3, "level": 3, "name": "光伏系统工程师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 301003, "subLevelModelList": [{"code": 6666600323, "positionType": 4, "level": 4, "name": "电力运维工程师"}, {"code": 6666601000, "positionType": 4, "level": 4, "name": "光伏系统安装工"}], "positionType": 3, "level": 3, "name": "风电/光伏运维工程师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 301004, "subLevelModelList": [{"code": 6666600768, "positionType": 4, "level": 4, "name": "管道储运工程师"}], "positionType": 3, "level": 3, "name": "水利工程师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 301001, "positionType": 3, "level": 3, "name": "地质工程师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "能源/地质", "searchKey": "nengyuanhuanbaonongye"}, {"code": 2600612, "subLevelModelList": [{"code": 300903, "subLevelModelList": [{"code": 6666600271, "positionType": 4, "level": 4, "name": "HSE主管（健康安全环保）"}, {"code": 6666600318, "positionType": 4, "level": 4, "name": "环保技术研发工程师"}, {"code": 6666600406, "positionType": 4, "level": 4, "name": "安全工程师"}], "positionType": 3, "level": 3, "name": "EHS工程师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 300905, "subLevelModelList": [{"code": 6666600105, "positionType": 4, "level": 4, "name": "检测员"}, {"code": 6666600229, "positionType": 4, "level": 4, "name": "微生物检测员"}, {"code": 6666600659, "positionType": 4, "level": 4, "name": "预防性维护工程师"}, {"code": 6666600725, "positionType": 4, "level": 4, "name": "质量检测员​"}], "positionType": 3, "level": 3, "name": "环境采样/检测员", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 300902, "subLevelModelList": [{"code": 6666600723, "positionType": 4, "level": 4, "name": "质量体系工程师"}], "positionType": 3, "level": 3, "name": "环评工程师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 300901, "subLevelModelList": [{"code": 6666600009, "positionType": 4, "level": 4, "name": "环保咨询顾问"}, {"code": 6666600165, "positionType": 4, "level": 4, "name": "环境工程师"}, {"code": 6666600176, "positionType": 4, "level": 4, "name": "环保项目商务经理"}, {"code": 6666600390, "positionType": 4, "level": 4, "name": "环保工程师"}, {"code": 6666600434, "positionType": 4, "level": 4, "name": "环境技术研发工程师"}, {"code": 6666600540, "positionType": 4, "level": 4, "name": "工程环保专员"}, {"code": 6666600728, "positionType": 4, "level": 4, "name": "再制造工程师"}], "positionType": 3, "level": 3, "name": "环保工程师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 300904, "subLevelModelList": [{"code": 6666600472, "positionType": 4, "level": 4, "name": "销售碳中和策略专员"}, {"code": 6666601097, "positionType": 4, "level": 4, "name": "环保管理员"}], "positionType": 3, "level": 3, "name": "碳排放管理师", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "环保", "searchKey": "nengyuanhuanbaonongye"}, {"code": 1000950, "subLevelModelList": [{"code": 400101, "subLevelModelList": [{"code": 6666600415, "positionType": 4, "level": 4, "name": "中药材种植技术员"}, {"code": 6666600550, "positionType": 4, "level": 4, "name": "主要农作物种子生产"}, {"code": 6666600704, "positionType": 4, "level": 4, "name": "种植技术员"}, {"code": 6666600793, "positionType": 4, "level": 4, "name": "茶叶种植技术员"}, {"code": 6666600861, "positionType": 4, "level": 4, "name": "蔬菜种植技术员"}, {"code": 6666600912, "positionType": 4, "level": 4, "name": "农业技术推广专员"}, {"code": 6666601056, "positionType": 4, "level": 4, "name": "蔬菜种苗培育技术员"}, {"code": 6666601075, "positionType": 4, "level": 4, "name": "农业技术员"}, {"code": 6666601078, "positionType": 4, "level": 4, "name": "农业种养殖技术研发员"}], "positionType": 3, "level": 3, "name": "农业/林业技术员", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 400201, "positionType": 3, "level": 3, "name": "饲养员", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 400202, "subLevelModelList": [{"code": 6666600046, "positionType": 4, "level": 4, "name": "水产养殖技术员"}, {"code": 6666600167, "positionType": 4, "level": 4, "name": "水产养殖区域经理（华东区）"}, {"code": 6666600382, "positionType": 4, "level": 4, "name": "水产基因育种研究员"}], "positionType": 3, "level": 3, "name": "养殖技术员", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}, {"code": 400203, "positionType": 3, "level": 3, "name": "畜牧兽医", "searchKey": "nengyuanhuanbaonongye", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "农/林/牧/渔", "searchKey": "nengyuanhuanbaonongye"}], "positionType": 0, "level": 1, "name": "能源/环保/农业", "searchKey": "nengyuanhuanbaonongye"}, {"code": 1000000, "subLevelModelList": [{"code": 1000010, "subLevelModelList": [{"code": 150407, "subLevelModelList": [{"code": 6666600240, "positionType": 4, "level": 4, "name": "食品公司总经理"}, {"code": 6666600285, "positionType": 4, "level": 4, "name": "公司总经理"}, {"code": 6666600859, "positionType": 4, "level": 4, "name": "研发中心总经理"}], "positionType": 3, "level": 3, "name": "总裁/总经理/CEO", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}, {"code": 150408, "positionType": 3, "level": 3, "name": "副总裁/副总经理/VP", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}, {"code": 150411, "subLevelModelList": [{"code": 6666600283, "positionType": 4, "level": 4, "name": "总经理助理"}], "positionType": 3, "level": 3, "name": "总助/CEO助理/董事长助理", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}, {"code": 150410, "subLevelModelList": [{"code": 6666600321, "positionType": 4, "level": 4, "name": "区域租赁经理"}, {"code": 6666600451, "positionType": 4, "level": 4, "name": "食品销售区域经理"}], "positionType": 3, "level": 3, "name": "区域负责人(辖多个分公司)", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}, {"code": 150409, "positionType": 3, "level": 3, "name": "分公司/代表处负责人", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}, {"code": 150414, "positionType": 3, "level": 3, "name": "董事会秘书", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}, {"code": 150413, "positionType": 3, "level": 3, "name": "联合创始人", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}, {"code": 150499, "positionType": 3, "level": 3, "name": "高级管理职位", "searchKey": "gaojiguanli", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "高级管理职位", "searchKey": "gaojiguanli"}], "positionType": 0, "level": 1, "name": "高级管理", "searchKey": "gaojiguanli"}, {"code": 1200000, "subLevelModelList": [{"code": 1001220, "subLevelModelList": [{"code": 200101, "subLevelModelList": [{"code": 6666600786, "positionType": 4, "level": 4, "name": "信息技术支持专员"}], "positionType": 3, "level": 3, "name": "其他职位", "searchKey": "qita", "recruitmentType": "1,2,3"}], "positionType": 0, "level": 2, "name": "其他职位类别", "searchKey": "qita"}], "positionType": 0, "level": 1, "name": "其他", "searchKey": "qita"}]}, "message": "Success"}